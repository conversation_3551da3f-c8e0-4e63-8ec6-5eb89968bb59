// Custom hook for Paytm payment integration
import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import {
  PaytmTransactionRequest,
  PaytmInitiateResponse,
  PaytmTransactionResponse,
  loadPaytmScript,
  getPaytmConfig,
  validatePaytmConfig
} from '@/lib/paytm';
import {
  validatePaymentRequest,
  retryPaymentVerification,
  trackPaymentEvent,
  getPaymentErrorMessage,
  isNetworkError,
  savePaymentState,
  clearPaymentState
} from '@/lib/payments';

interface UsePaytmOptions {
  onSuccess?: (response: PaytmTransactionResponse) => void;
  onFailure?: (error: string) => void;
  onCancel?: () => void;
}

interface PaytmCheckoutConfig {
  txnToken: string;
  orderId: string;
  amount: string;
  merchantId: string;
  isStaging: boolean;
  callbackUrl: string;
}

export const usePaytm = (options: UsePaytmOptions = {}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [checkoutConfig, setCheckoutConfig] = useState<PaytmCheckoutConfig | null>(null);

  // Initiate payment mutation
  const initiateMutation = useMutation({
    mutationFn: async (request: PaytmTransactionRequest): Promise<PaytmInitiateResponse> => {
      // Validate user authentication
      if (!user) {
        trackPaymentEvent('payment_initiation_failed', { reason: 'user_not_authenticated' });
        throw new Error('User not authenticated');
      }

      // Validate Paytm configuration
      if (!validatePaytmConfig()) {
        trackPaymentEvent('payment_initiation_failed', { reason: 'invalid_config' });
        throw new Error('Paytm configuration is invalid');
      }

      // Validate payment request
      const validation = validatePaymentRequest({
        amount: parseFloat(request.amount),
        orderId: request.orderId,
        customerId: request.customerId,
        customerEmail: request.customerEmail,
        customerPhone: request.customerPhone,
      });

      if (!validation.isValid) {
        trackPaymentEvent('payment_initiation_failed', {
          reason: 'validation_failed',
          errors: validation.errors
        });
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Save payment state for recovery
      savePaymentState(request.orderId, {
        status: 'initiating',
        request,
        userId: user.id
      });

      trackPaymentEvent('payment_initiation_started', {
        orderId: request.orderId,
        amount: request.amount,
        paymentModes: request.paymentModes
      });

      const { data, error } = await supabase.functions.invoke('paytm-initiate', {
        body: {
          amount: request.amount,
          orderId: request.orderId,
          customerId: request.customerId,
          customerEmail: request.customerEmail,
          customerPhone: request.customerPhone,
          paymentModes: request.paymentModes,
        },
      });

      if (error) {
        trackPaymentEvent('payment_initiation_failed', {
          reason: 'api_error',
          error: error.message
        });
        throw new Error(getPaymentErrorMessage(error));
      }

      if (!data.success) {
        trackPaymentEvent('payment_initiation_failed', {
          reason: 'api_response_error',
          error: data.error
        });
        throw new Error(data.error || 'Failed to initiate payment');
      }

      trackPaymentEvent('payment_initiation_success', {
        orderId: data.orderId,
        amount: data.amount
      });

      return data;
    },
    onSuccess: (data) => {
      setCheckoutConfig({
        txnToken: data.txnToken!,
        orderId: data.orderId,
        amount: data.amount,
        merchantId: data.merchantId!,
        isStaging: data.isStaging!,
        callbackUrl: data.callbackUrl!,
      });
    },
    onError: (error) => {
      toast.error(`Payment initiation failed: ${error.message}`);
      options.onFailure?.(error.message);
    },
  });

  // Verify payment mutation
  const verifyMutation = useMutation({
    mutationFn: async (orderId: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.functions.invoke('paytm-verify', {
        body: { orderId },
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to verify payment');
      }

      return data;
    },
    onSuccess: (data) => {
      if (data.status === 'success') {
        toast.success('Payment successful!');
        queryClient.invalidateQueries({ queryKey: ['wallet'] });
        queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });
        options.onSuccess?.(data as PaytmTransactionResponse);
      } else if (data.status === 'failed') {
        toast.error(`Payment failed: ${data.respMsg}`);
        options.onFailure?.(data.respMsg);
      } else {
        toast.info('Payment is still pending');
      }
    },
    onError: (error) => {
      toast.error(`Payment verification failed: ${error.message}`);
      options.onFailure?.(error.message);
    },
  });

  // Open Paytm checkout
  const openCheckout = useCallback(async () => {
    if (!checkoutConfig) {
      throw new Error('Checkout configuration not available');
    }

    try {
      setIsLoading(true);
      
      // Load Paytm script
      await loadPaytmScript();

      // Check if Paytm object is available
      if (typeof window !== 'undefined' && (window as any).Paytm) {
        const config = {
          root: '',
          flow: 'DEFAULT',
          data: {
            orderId: checkoutConfig.orderId,
            token: checkoutConfig.txnToken,
            tokenType: 'TXN_TOKEN',
            amount: checkoutConfig.amount,
          },
          handler: {
            notifyMerchant: (eventName: string, data: any) => {
              console.log('Paytm notification:', eventName, data);
              
              if (eventName === 'APP_CLOSED') {
                options.onCancel?.();
              }
            },
          },
        };

        const paytm = new (window as any).Paytm(config);
        paytm.show();
      } else {
        throw new Error('Paytm script not loaded properly');
      }
    } catch (error) {
      console.error('Error opening Paytm checkout:', error);
      toast.error('Failed to open payment gateway');
      options.onFailure?.(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [checkoutConfig, options]);

  // Start payment process
  const startPayment = useCallback(async (request: PaytmTransactionRequest) => {
    try {
      setIsLoading(true);
      await initiateMutation.mutateAsync(request);
    } catch (error) {
      console.error('Error starting payment:', error);
    } finally {
      setIsLoading(false);
    }
  }, [initiateMutation]);

  // Verify payment
  const verifyPayment = useCallback(async (orderId: string) => {
    try {
      setIsLoading(true);
      await verifyMutation.mutateAsync(orderId);
    } catch (error) {
      console.error('Error verifying payment:', error);
    } finally {
      setIsLoading(false);
    }
  }, [verifyMutation]);

  // Reset checkout state
  const resetCheckout = useCallback(() => {
    setCheckoutConfig(null);
    initiateMutation.reset();
    verifyMutation.reset();
  }, [initiateMutation, verifyMutation]);

  return {
    // State
    isLoading: isLoading || initiateMutation.isPending || verifyMutation.isPending,
    checkoutConfig,
    isInitiated: !!checkoutConfig,
    
    // Actions
    startPayment,
    openCheckout,
    verifyPayment,
    resetCheckout,
    
    // Mutation states
    initiateError: initiateMutation.error,
    verifyError: verifyMutation.error,
    isInitiating: initiateMutation.isPending,
    isVerifying: verifyMutation.isPending,
  };
};
