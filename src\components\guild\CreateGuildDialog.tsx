
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { icons } from 'lucide-react';

const guildSchema = z.object({
  name: z.string().min(3, 'Guild name must be at least 3 characters long.').max(50, 'Guild name must be 50 characters or less.'),
  icon_name: z.string().min(1, 'Please select an icon.'),
});

const availableIcons = ['Zap', 'Cpu', 'Waves', 'Database', 'Atom', 'Shield', 'Swords', 'Gem'];

export function CreateGuildDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof guildSchema>>({
    resolver: zodResolver(guildSchema),
    defaultValues: {
      name: '',
      icon_name: '',
    },
  });

  const createGuildMutation = useMutation({
    mutationFn: async (values: z.infer<typeof guildSchema>) => {
      return new Promise<{ latitude: number; longitude: number }>((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('Geolocation is not supported by your browser.'));
          return;
        }
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          },
          () => {
            // Default to a null location if permission is denied
            resolve({ latitude: 0, longitude: 0 });
            toast({
                variant: "destructive",
                title: "Location Access Denied",
                description: "Cannot get location. Guild will be created without location data.",
            });
          }
        );
      }).then(async (location) => {
        const { data, error } = await supabase.rpc('create_guild', {
          p_name: values.name,
          p_icon_name: values.icon_name,
          p_latitude: location.latitude,
          p_longitude: location.longitude,
        });

        if (error) {
          throw new Error(error.message);
        }
        return data;
      });
    },
    onSuccess: () => {
      toast({
        title: 'Guild created!',
        description: 'You are now the leader of your new guild.',
      });
      queryClient.invalidateQueries({ queryKey: ['userGuild', user?.id] });
      setIsOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Error creating guild',
        description: error.message.includes('duplicate key value violates unique constraint "guilds_name_key"')
            ? "A guild with this name already exists. Please choose another name."
            : error.message,
      });
    },
  });

  const onSubmit = (values: z.infer<typeof guildSchema>) => {
    createGuildMutation.mutate(values);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Create Guild</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create a New Guild</DialogTitle>
          <DialogDescription>
            Choose a name and an icon for your guild. You will become the leader.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Guild Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Neon Runners" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="icon_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-4 gap-4">
                      {availableIcons.map((iconName) => {
                        const LucideIcon = icons[iconName as keyof typeof icons];
                        const isSelected = field.value === iconName;
                        return (
                          <button
                            type="button"
                            key={iconName}
                            onClick={() => field.onChange(iconName)}
                            className={`flex justify-center items-center p-4 rounded-lg border-2 transition-all ${
                              isSelected ? 'border-primary ring-2 ring-primary bg-primary/10' : 'border-input hover:border-primary/50'
                            }`}
                          >
                            <LucideIcon className={`h-8 w-8 ${isSelected ? 'text-primary' : ''}`} />
                          </button>
                        );
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit" disabled={createGuildMutation.isPending}>
                {createGuildMutation.isPending ? 'Creating...' : 'Create Guild'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
