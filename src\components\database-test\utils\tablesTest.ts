// Other tables testing utilities
import { supabase } from '@/integrations/supabase/client';
import { TestResult, DatabaseTestConfig } from '../types';
import { Users, MessageSquare, Wallet, Trophy } from 'lucide-react';

export const testOtherTables = async (
  addResult: (result: Omit<TestResult, 'timestamp'>) => void
) => {
  const tables: DatabaseTestConfig[] = [
    { name: 'guilds', icon: Users },
    { name: 'messages', icon: MessageSquare },
    { name: 'wallets', icon: Wallet },
    { name: 'user_leaderboard_stats', icon: Trophy },
  ];

  for (const table of tables) {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);
      
      if (error) {
        addResult({
          table: table.name,
          operation: 'READ',
          status: 'error',
          message: error.message,
          duration: Date.now() - startTime,
        });
      } else {
        addResult({
          table: table.name,
          operation: 'READ',
          status: 'success',
          message: `Table accessible (${data?.length || 0} records)`,
          duration: Date.now() - startTime,
        });
      }
    } catch (error) {
      addResult({
        table: table.name,
        operation: 'READ',
        status: 'error',
        message: `Unexpected error: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  }
};
