// Test results list component
import { CheckCircle } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { TestResult } from './types';
import { TestResultItem } from './TestResultItem';

interface TestResultsListProps {
  testResults: TestResult[];
}

export function TestResultsList({ testResults }: TestResultsListProps) {
  if (testResults.length === 0) {
    return null;
  }

  return (
    <GlassCard className="p-6">
      <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
        <CheckCircle className="h-5 w-5" />
        Test Results ({testResults.length})
      </h3>
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {testResults.map((result, index) => (
          <TestResultItem key={index} result={result} />
        ))}
      </div>
    </GlassCard>
  );
}
