# How to Apply the Chat Database Migration

## Overview
This document explains how to apply the database migration that adds the missing `channel_type` and `guild_id` columns to the `messages` table.

## Prerequisites
- Access to Supabase dashboard or CLI
- Database admin permissions
- The migration file: `supabase/migrations/20250703000000_fix_messages_table_schema.sql`

## Method 1: Using Supabase Dashboard (Recommended)

1. **Login to Supabase Dashboard**
   - Go to https://supabase.com/dashboard
   - Login to your account
   - Navigate to your project: `passdown_ecommerce`

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Execute Migration**
   - Copy the contents of `supabase/migrations/20250703000000_fix_messages_table_schema.sql`
   - Paste into the SQL editor
   - Click "Run" to execute

4. **Verify Changes**
   - Go to "Table Editor" → "messages"
   - Confirm `channel_type` and `guild_id` columns are present

## Method 2: Using Supabase CLI

1. **Install Supabase CLI** (if not already installed)
   ```bash
   npm install -g supabase
   ```

2. **<PERSON>gin and Link Project**
   ```bash
   supabase login
   supabase link --project-ref xutzuilymxmhcqhnohwq
   ```

3. **Apply Migration**
   ```bash
   supabase db push
   ```

## Method 3: Using the Provided Script

1. **Set Environment Variable**
   ```bash
   export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"
   ```

2. **Run Migration Script**
   ```bash
   node apply-migration.js
   ```

## After Migration: Revert Temporary Code Changes

Once the migration is applied, revert the temporary fixes:

### 1. Update Chat.tsx - Restore channel_type filtering
```typescript
// In fetchGlobalMessages function
const { data, error } = await supabase
  .from('messages')
  .select('*, profiles(username, avatar_url)')
  .eq('channel_type', 'global')  // ✅ Restore this line
  .order('created_at', { ascending: true })
  .limit(100);

// In message insert
const { error } = await supabase.from('messages').insert({
  content: newContent,
  user_id: user.id,
  channel_type: 'global',  // ✅ Restore this line
});
```

### 2. Update types.ts - Restore column definitions
```typescript
messages: {
  Row: {
    content: string
    created_at: string
    id: string
    user_id: string
    channel_type: string      // ✅ Restore this
    guild_id: string | null   // ✅ Restore this
  }
  // ... similar for Insert and Update
}
```

## Verification Steps

1. **Check Database Schema**
   - Verify `messages` table has `channel_type` and `guild_id` columns
   - Confirm existing data is preserved

2. **Test Chat Functionality**
   - Navigate to `/chat` in the application
   - Verify messages load without errors
   - Test sending new messages
   - Check that new messages have `channel_type = 'global'`

3. **Check Console for Errors**
   - Ensure no database errors in browser console
   - Verify API calls succeed

## Rollback Plan (If Needed)

If issues occur after migration:

1. **Immediate Rollback**
   ```sql
   ALTER TABLE public.messages DROP COLUMN IF EXISTS channel_type;
   ALTER TABLE public.messages DROP COLUMN IF EXISTS guild_id;
   ```

2. **Restore Temporary Fix**
   - Keep the current code changes that remove channel_type references
   - Chat will continue working as it does now

## Notes
- The migration is designed to be safe and backward-compatible
- Existing messages will be preserved
- New messages will automatically get `channel_type = 'global'`
- Guild chat functionality will be enabled once guild system is complete
