
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Profile from "./pages/Profile";
import Leaderboard from "./pages/Leaderboard";
import Guild from "./pages/Guild";
import Achievements from "./pages/Achievements";
import AuthPage from "./pages/Auth";
import Admin from "./pages/Admin";
import Wallet from "./pages/Wallet";
import Payment from "./pages/Payment";
import PaymentCallback from "./pages/PaymentCallback";

import { AuthProvider } from "./contexts/AuthContext";
import { ProtectedRoute } from "./components/ProtectedRoute";
import ChatPage from "./pages/Chat";
import TrackRun from "./pages/TrackRun";
import TrackingTest from "./pages/TrackingTest";
import DatabaseTest from "./pages/DatabaseTest";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { DevTools } from "./components/DevTools";
import { isDevelopment } from "./lib/config";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/auth" element={<AuthPage />} />
            
            <Route element={<ProtectedRoute />}>
              <Route path="/profile" element={<Profile />} />
              <Route path="/leaderboard" element={<Leaderboard />} />
              <Route path="/guild" element={<Guild />} />
              <Route path="/achievements" element={<Achievements />} />
              <Route path="/wallet" element={<Wallet />} />
              <Route path="/payment" element={<Payment />} />
              <Route path="/payment/callback" element={<PaymentCallback />} />

              <Route path="/chat" element={<ChatPage />} />
              <Route path="/track-run" element={<TrackRun />} />
              {isDevelopment() && <Route path="/tracking-test" element={<TrackingTest />} />}
              {isDevelopment() && <Route path="/database-test" element={<DatabaseTest />} />}
            </Route>

            <Route element={<ProtectedRoute adminOnly />}>
              <Route path="/admin" element={<Admin />} />
            </Route>

            <Route path="*" element={<NotFound />} />
          </Routes>
          <DevTools />
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
