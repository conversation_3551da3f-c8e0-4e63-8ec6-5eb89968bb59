@echo off
echo ========================================
echo SoloGrind Project Status Checker
echo ========================================
echo.

echo [1/5] Checking Node.js and npm...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
) else (
    echo ✅ Node.js: 
    node --version
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found.
    pause
    exit /b 1
) else (
    echo ✅ npm: 
    npm --version
)

echo.
echo [2/5] Checking project dependencies...
if not exist "node_modules" (
    echo ⚠️  Dependencies not installed. Installing now...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies.
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependencies installed
)

echo.
echo [3/5] Checking environment configuration...
if exist ".env.development" (
    echo ✅ Development environment configured
) else (
    echo ❌ Missing .env.development file
)

if exist ".env.production" (
    echo ✅ Production environment configured
) else (
    echo ❌ Missing .env.production file
)

echo.
echo [4/5] Checking TypeScript configuration...
if exist "tsconfig.json" (
    echo ✅ TypeScript configuration found
) else (
    echo ❌ Missing TypeScript configuration
)

echo.
echo [5/5] Checking build status...
echo Testing TypeScript compilation...
npx tsc --noEmit
if %errorlevel% neq 0 (
    echo ❌ TypeScript compilation errors found
    echo Please fix the errors before continuing.
    pause
    exit /b 1
) else (
    echo ✅ TypeScript compilation successful
)

echo.
echo ========================================
echo 🎉 PROJECT STATUS: READY FOR DEVELOPMENT
echo ========================================
echo.
echo Your SoloGrind project is properly configured and ready to run!
echo.
echo To start development:
echo   1. Run: start-dev.bat (development server only)
echo   2. Run: start-browser.bat (server + auto-open browser)
echo   3. Manual: npm run dev
echo.
echo Development URLs:
echo   • Main App: http://localhost:8080
echo   • Analytics Test: http://localhost:8080/tracking-test
echo   • Database Test: http://localhost:8080/database-test
echo.
echo ========================================
pause
