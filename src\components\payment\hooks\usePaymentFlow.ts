import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { PaymentStep } from '../types';

export function usePaymentFlow() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState<PaymentStep>('select');
  const [orderId, setOrderId] = useState<string | null>(null);

  // Check URL parameters for payment status
  useEffect(() => {
    const orderIdParam = searchParams.get('orderId');
    const statusParam = searchParams.get('status');
    
    if (orderIdParam) {
      setOrderId(orderIdParam);
      if (statusParam === 'success') {
        setCurrentStep('success');
      } else if (statusParam === 'failure') {
        setCurrentStep('failure');
      } else {
        setCurrentStep('status');
      }
    }
  }, [searchParams]);

  const handlePaymentSuccess = () => {
    setCurrentStep('success');
    toast.success('Payment completed successfully!');
  };

  const handlePaymentFailure = (error: string) => {
    setCurrentStep('failure');
    toast.error(`Payment failed: ${error}`);
  };

  const handleBack = () => {
    if (currentStep === 'select') {
      navigate('/wallet');
    } else {
      setCurrentStep('select');
      setOrderId(null);
      // Clear URL parameters
      navigate('/payment', { replace: true });
    }
  };

  const handleAddMoneySuccess = () => {
    toast.success('Money added to wallet successfully!');
    navigate('/wallet');
  };

  return {
    currentStep,
    orderId,
    handlePaymentSuccess,
    handlePaymentFailure,
    handleBack,
    handleAddMoneySuccess,
  };
}
