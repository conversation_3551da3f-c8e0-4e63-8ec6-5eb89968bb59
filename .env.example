# App Configuration
VITE_APP_NAME=SoloGrind
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# Feature Flags
VITE_ENABLE_SUBSCRIPTIONS=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PAYMENTS=true
VITE_DEBUG_MODE=true
VITE_TEST_MODE=false

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Analytics Configuration
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Payment Configuration - Stripe (existing)
VITE_STRIPE_PUBLISHABLE_KEY=

# Payment Configuration - Paytm
VITE_PAYTM_MERCHANT_ID=your_paytm_merchant_id
VITE_PAYTM_STAGING=true
VITE_PAYTM_CALLBACK_URL=http://localhost:8080/payment/callback
VITE_PAYTM_WEBSITE=WEBSTAGING
VITE_PAYTM_INDUSTRY_TYPE=Retail
VITE_PAYTM_CHANNEL_ID=WEB

# Development Configuration
VITE_DEV_SERVER_PORT=8080
VITE_DEV_SERVER_HOST=localhost

# Supabase Edge Functions Environment Variables (for backend)
# These should be set in your Supabase project settings, not in this file
# PAYTM_MERCHANT_ID=your_paytm_merchant_id
# PAYTM_MERCHANT_KEY=your_paytm_merchant_key_secret
# PAYTM_WEBSITE=WEBSTAGING
# PAYTM_INDUSTRY_TYPE=Retail
# PAYTM_CHANNEL_ID=WEB
# PAYTM_CALLBACK_URL=https://your-domain.com/payment/callback
# PAYTM_STAGING=true
