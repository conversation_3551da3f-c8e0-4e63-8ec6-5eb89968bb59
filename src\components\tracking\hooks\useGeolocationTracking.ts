import { useRef, useCallback } from 'react';
import { getDistanceFromLatLonInKm } from '@/lib/geo';
import { useTracking } from '@/hooks/useTracking';
import { logUserAction, logError } from '@/lib/logger';
import { GeolocationCoords } from '../types';

interface UseGeolocationTrackingProps {
  onDistanceUpdate: (distance: number) => void;
  onError: (error: string) => void;
  onStop: (shouldSave?: boolean) => void;
}

export function useGeolocationTracking({
  onDistanceUpdate,
  onError,
  onStop,
}: UseGeolocationTrackingProps) {
  const { trackButtonClick, trackError } = useTracking();
  const watchId = useRef<number | null>(null);
  const lastPosition = useRef<GeolocationCoords | null>(null);
  const timerInterval = useRef<ReturnType<typeof setInterval> | null>(null);

  const startTracking = useCallback((onDurationUpdate: (duration: number) => void) => {
    if (!navigator.geolocation) {
      const errorMsg = "Geolocation is not supported by your browser.";
      onError(errorMsg);
      trackError(errorMsg, { feature: 'geolocation' });
      logError('Geolocation not supported', { userAgent: navigator.userAgent });
      return;
    }

    trackButtonClick('start_workout', { workout_type: 'run' });
    logUserAction('Started workout tracking', { type: 'run' });

    // Reset position tracking
    lastPosition.current = null;

    // Start timer
    timerInterval.current = setInterval(() => {
      onDurationUpdate(d => d + 1);
    }, 1000);

    // Start geolocation tracking
    watchId.current = navigator.geolocation.watchPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        if (lastPosition.current) {
          const newDistance = getDistanceFromLatLonInKm(
            lastPosition.current.latitude,
            lastPosition.current.longitude,
            latitude,
            longitude
          );
          onDistanceUpdate(newDistance);
        }
        lastPosition.current = { latitude, longitude };
      },
      (err) => {
        onError(`Error getting location: ${err.message}`);
        onStop(false); // Stop tracking on error
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  }, [onDistanceUpdate, onError, onStop, trackButtonClick, trackError]);

  const stopTracking = useCallback(() => {
    if (watchId.current) {
      navigator.geolocation.clearWatch(watchId.current);
      watchId.current = null;
    }
    if (timerInterval.current) {
      clearInterval(timerInterval.current);
      timerInterval.current = null;
    }
  }, []);

  const cleanup = useCallback(() => {
    stopTracking();
  }, [stopTracking]);

  return {
    startTracking,
    stopTracking,
    cleanup,
  };
}
