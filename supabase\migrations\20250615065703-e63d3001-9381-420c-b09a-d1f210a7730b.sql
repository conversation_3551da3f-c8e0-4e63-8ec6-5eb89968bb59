
-- Helper function to determine cashback rate based on distance
CREATE OR REPLACE FUNCTION public.get_cashback_base(distance_km NUMERIC)
RETURNS NUMERIC
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    IF distance_km >= 5 THEN RETURN 0.90;
    ELSIF distance_km >= 4 THEN RETURN 0.80;
    ELSIF distance_km >= 3 THEN RETURN 0.70;
    ELSIF distance_km >= 2 THEN RETURN 0.60;
    ELSIF distance_km >= 1 THEN RETURN 0.50;
    ELSE RETURN 0;
    END IF;
END;
$$;

-- Function to calculate cashback for all users over the last 30 days
CREATE OR REPLACE FUNCTION public.calculate_monthly_cashback()
RETURNS TABLE(username TEXT, cashback_percentage NUMERIC)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.username,
        (SUM(public.get_cashback_base(ua.distance_km)) / 30.0) * 100.0 AS cashback_percentage
    FROM
        public.user_activities ua
    JOIN
        public.profiles p ON ua.user_id = p.id
    WHERE
        ua.activity_date >= (CURRENT_DATE - INTERVAL '30 days')
        AND p.username IS NOT NULL
    GROUP BY
        p.username
    ORDER BY
        cashback_percentage DESC;
END;
$$;
