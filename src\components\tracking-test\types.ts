// Types for tracking test functionality
export interface TrackingTestResult {
  timestamp: string;
  message: string;
  status: 'success' | 'error' | 'info';
}

export interface EnvironmentInfo {
  environment: string;
  features: {
    analytics: boolean;
    tracking: boolean;
    debug: boolean;
  };
}

export interface TestFunction {
  name: string;
  description: string;
  execute: () => Promise<void> | void;
}
