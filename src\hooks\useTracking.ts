// React hook for tracking and analytics
import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { 
  trackPageView, 
  trackUserAction, 
  trackWorkout, 
  trackAchievement, 
  trackGuildAction,
  trackSubscription,
  identifyUser 
} from '@/lib/analytics';
import { 
  logUserAction, 
  logWorkout, 
  logGuildActivity, 
  logSubscriptionEvent 
} from '@/lib/logger';

export const useTracking = () => {
  const location = useLocation();
  const { user, profile } = useAuth();

  // Track page views automatically
  useEffect(() => {
    const pageName = getPageName(location.pathname);
    trackPageView(pageName, {
      path: location.pathname,
      search: location.search,
      hash: location.hash,
    });
  }, [location]);

  // Identify user when they log in
  useEffect(() => {
    if (user && profile) {
      identifyUser({
        userId: user.id,
        username: profile.username || undefined,
        email: user.email || undefined,
        joinDate: new Date(user.created_at),
      });
    }
  }, [user, profile]);

  // Helper function to get page name from pathname
  const getPageName = (pathname: string): string => {
    const routes: Record<string, string> = {
      '/': 'Home',
      '/auth': 'Authentication',
      '/profile': 'Profile',
      '/leaderboard': 'Leaderboard',
      '/guild': 'Guild',
      '/achievements': 'Achievements',
      '/wallet': 'Wallet',
      '/subscription': 'Subscription',
      '/chat': 'Chat',
      '/track-run': 'Track Run',
      '/admin': 'Admin',
    };

    return routes[pathname] || 'Unknown Page';
  };

  // Track user actions
  const trackAction = useCallback((action: string, properties?: Record<string, any>) => {
    trackUserAction(action, {
      userId: user?.id,
      username: profile?.username,
      ...properties,
    });
    logUserAction(action, properties);
  }, [user, profile]);

  // Track workout completion
  const trackWorkoutCompletion = useCallback((workoutData: {
    type: string;
    distance?: number;
    duration?: number;
    calories?: number;
  }) => {
    trackWorkout({
      ...workoutData,
    });
    logWorkout(workoutData.type, workoutData);
    
    // Also track as user action
    trackAction('workout_completed', workoutData);
  }, [trackAction]);

  // Track achievement unlock
  const trackAchievementUnlock = useCallback((achievementData: {
    achievementId: string;
    achievementName: string;
    tier?: string;
  }) => {
    trackAchievement(achievementData);
    trackAction('achievement_unlocked', achievementData);
  }, [trackAction]);

  // Track guild activities
  const trackGuildActivity = useCallback((action: string, guildData?: {
    guildId?: string;
    guildName?: string;
  }) => {
    trackGuildAction(action, guildData);
    logGuildActivity(action, guildData);
    trackAction(`guild_${action}`, guildData);
  }, [trackAction]);

  // Track subscription events
  const trackSubscriptionEvent = useCallback((action: string, subscriptionData?: {
    plan?: string;
    amount?: number;
  }) => {
    trackSubscription(action, subscriptionData);
    logSubscriptionEvent(action, subscriptionData);
    trackAction(`subscription_${action}`, subscriptionData);
  }, [trackAction]);

  // Track button clicks
  const trackButtonClick = useCallback((buttonName: string, properties?: Record<string, any>) => {
    trackAction('button_click', {
      button_name: buttonName,
      ...properties,
    });
  }, [trackAction]);

  // Track form submissions
  const trackFormSubmission = useCallback((formName: string, properties?: Record<string, any>) => {
    trackAction('form_submission', {
      form_name: formName,
      ...properties,
    });
  }, [trackAction]);

  // Track navigation
  const trackNavigation = useCallback((from: string, to: string) => {
    trackAction('navigation', {
      from,
      to,
    });
  }, [trackAction]);

  // Track search
  const trackSearch = useCallback((query: string, results?: number) => {
    trackAction('search', {
      query,
      results_count: results,
    });
  }, [trackAction]);

  // Track social sharing
  const trackShare = useCallback((content: string, platform?: string) => {
    trackAction('share', {
      content,
      platform,
    });
  }, [trackAction]);

  // Track errors
  const trackError = useCallback((error: string, context?: Record<string, any>) => {
    trackAction('error_occurred', {
      error_message: error,
      ...context,
    });
  }, [trackAction]);

  // Track performance metrics
  const trackPerformanceMetric = useCallback((metric: string, value: number, context?: Record<string, any>) => {
    trackAction('performance_metric', {
      metric,
      value,
      ...context,
    });
  }, [trackAction]);

  return {
    // Basic tracking
    trackAction,
    trackButtonClick,
    trackFormSubmission,
    trackNavigation,
    trackSearch,
    trackShare,
    trackError,
    trackPerformanceMetric,
    
    // App-specific tracking
    trackWorkoutCompletion,
    trackAchievementUnlock,
    trackGuildActivity,
    trackSubscriptionEvent,
  };
};

// Hook for tracking component mount/unmount
export const useComponentTracking = (componentName: string) => {
  useEffect(() => {
    trackUserAction('component_mounted', { component: componentName });
    
    return () => {
      trackUserAction('component_unmounted', { component: componentName });
    };
  }, [componentName]);
};

// Hook for tracking time spent on page
export const useTimeTracking = (pageName: string) => {
  useEffect(() => {
    const startTime = Date.now();
    
    return () => {
      const timeSpent = Date.now() - startTime;
      trackUserAction('time_on_page', {
        page: pageName,
        time_spent_ms: timeSpent,
        time_spent_seconds: Math.round(timeSpent / 1000),
      });
    };
  }, [pageName]);
};

// Hook for tracking scroll depth
export const useScrollTracking = (pageName: string) => {
  useEffect(() => {
    let maxScrollDepth = 0;
    
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      const scrollDepth = Math.round((scrollTop + windowHeight) / documentHeight * 100);
      
      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      
      if (maxScrollDepth > 0) {
        trackUserAction('scroll_depth', {
          page: pageName,
          max_scroll_depth: maxScrollDepth,
        });
      }
    };
  }, [pageName]);
};
