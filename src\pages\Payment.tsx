import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { BottomNav } from '@/components/BottomNav';
import { GlassCard } from '@/components/GlassCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Wallet, 
  CreditCard, 
  Shield, 
  Zap,
  IndianRupee,
  Clock,
  CheckCircle2
} from 'lucide-react';
import { AddMoneyDialog } from '@/components/payments/AddMoneyDialog';
import { PaymentStatus } from '@/components/payments/PaymentStatus';
import { useAuth } from '@/contexts/AuthContext';
import { isPaytmEnabled } from '@/lib/config';
import { toast } from 'sonner';

type PaymentStep = 'select' | 'status' | 'success' | 'failure';

export default function Payment() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [currentStep, setCurrentStep] = useState<PaymentStep>('select');
  const [orderId, setOrderId] = useState<string | null>(null);

  // Check URL parameters for payment status
  useEffect(() => {
    const orderIdParam = searchParams.get('orderId');
    const statusParam = searchParams.get('status');
    
    if (orderIdParam) {
      setOrderId(orderIdParam);
      if (statusParam === 'success') {
        setCurrentStep('success');
      } else if (statusParam === 'failure') {
        setCurrentStep('failure');
      } else {
        setCurrentStep('status');
      }
    }
  }, [searchParams]);

  const handlePaymentSuccess = () => {
    setCurrentStep('success');
    toast.success('Payment completed successfully!');
  };

  const handlePaymentFailure = (error: string) => {
    setCurrentStep('failure');
    toast.error(`Payment failed: ${error}`);
  };

  const handleBack = () => {
    if (currentStep === 'select') {
      navigate('/wallet');
    } else {
      setCurrentStep('select');
      setOrderId(null);
      // Clear URL parameters
      navigate('/payment', { replace: true });
    }
  };

  const handleAddMoneySuccess = () => {
    toast.success('Money added to wallet successfully!');
    navigate('/wallet');
  };

  if (!user) {
    return (
      <>
        <div
          style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
          className="fixed inset-0 bg-left-top bg-cover filter blur-sm scale-105"
        />
        <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
          <div className="px-4">
            <GlassCard className="p-6 text-center">
              <h1 className="text-xl font-bold text-white mb-2">Authentication Required</h1>
              <p className="text-white/60 mb-4">Please login to access payment features</p>
              <Button onClick={() => navigate('/auth')} className="bg-electric hover:bg-purple text-white">
                Login
              </Button>
            </GlassCard>
          </div>
          <BottomNav />
        </div>
      </>
    );
  }

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-left-top bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-4">
          {/* Header */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBack}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="gradient-title text-2xl">
                {currentStep === 'select' ? 'Add Money' : 'Payment Status'}
              </h1>
              <p className="text-white/60 text-sm">
                {currentStep === 'select' 
                  ? 'Add money to your wallet securely' 
                  : 'Track your payment progress'
                }
              </p>
            </div>
          </div>

          {/* Content based on current step */}
          {currentStep === 'select' && (
            <div className="space-y-4">
              {/* Payment Options */}
              <GlassCard className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-electric/20 rounded-lg">
                    <Wallet className="h-5 w-5 text-electric" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-white">Add Money to Wallet</h2>
                    <p className="text-white/60 text-sm">Choose your preferred payment method</p>
                  </div>
                </div>

                {isPaytmEnabled() ? (
                  <div className="space-y-4">
                    {/* Paytm Payment Option */}
                    <Card className="bg-white/5 border-white/10">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-blue-500/20 rounded-lg">
                              <CreditCard className="h-5 w-5 text-blue-400" />
                            </div>
                            <div>
                              <CardTitle className="text-white text-base">Paytm Payment Gateway</CardTitle>
                              <CardDescription className="text-white/60">
                                Pay using UPI, Cards, Net Banking, or Wallet
                              </CardDescription>
                            </div>
                          </div>
                          <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                            Recommended
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm text-white/60">
                            <Shield className="h-4 w-4 text-green-400" />
                            Secure
                          </div>
                          <div className="flex items-center gap-2 text-sm text-white/60">
                            <Zap className="h-4 w-4 text-yellow-400" />
                            Instant
                          </div>
                          <div className="flex items-center gap-2 text-sm text-white/60">
                            <CheckCircle2 className="h-4 w-4 text-blue-400" />
                            UPI Support
                          </div>
                        </div>
                        
                        <AddMoneyDialog onSuccess={handleAddMoneySuccess}>
                          <Button className="w-full bg-electric hover:bg-purple text-white">
                            <IndianRupee className="h-4 w-4 mr-2" />
                            Add Money via Paytm
                          </Button>
                        </AddMoneyDialog>
                      </CardContent>
                    </Card>

                    {/* Features */}
                    <div className="grid grid-cols-2 gap-3">
                      <Card className="bg-white/5 border-white/10 p-3">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-400" />
                          <span className="text-sm text-white">Bank-level Security</span>
                        </div>
                      </Card>
                      <Card className="bg-white/5 border-white/10 p-3">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-blue-400" />
                          <span className="text-sm text-white">Instant Processing</span>
                        </div>
                      </Card>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="p-4 bg-yellow-500/20 border border-yellow-500/50 rounded-lg mb-4">
                      <h3 className="font-semibold text-yellow-300 mb-2">Payment Gateway Unavailable</h3>
                      <p className="text-sm text-yellow-200/80">
                        Paytm payment gateway is currently not configured. Please contact support.
                      </p>
                    </div>
                  </div>
                )}
              </GlassCard>

              {/* Information */}
              <GlassCard className="p-4">
                <h3 className="font-semibold text-white mb-3">Payment Information</h3>
                <div className="space-y-2 text-sm text-white/60">
                  <p>• Minimum amount: ₹1</p>
                  <p>• Maximum amount: ₹1,00,000 per transaction</p>
                  <p>• Money will be added instantly to your wallet</p>
                  <p>• All transactions are secured with bank-level encryption</p>
                </div>
              </GlassCard>
            </div>
          )}

          {/* Payment Status */}
          {(currentStep === 'status' || currentStep === 'success' || currentStep === 'failure') && orderId && (
            <PaymentStatus
              orderId={orderId}
              onBack={handleBack}
              onSuccess={handlePaymentSuccess}
              onFailure={handlePaymentFailure}
              autoVerify={currentStep === 'status'}
            />
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
