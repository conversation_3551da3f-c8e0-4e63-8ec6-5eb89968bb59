#!/bin/bash

echo "========================================"
echo "    SoloGrind Development Server"
echo "    (Auto-open Browser Mode)"
echo "========================================"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "[INFO] Installing dependencies..."
    echo "This may take a few minutes..."
    npm install
    if [ $? -ne 0 ]; then
        echo "[ERROR] Failed to install dependencies!"
        exit 1
    fi
    echo "[SUCCESS] Dependencies installed successfully!"
    echo ""
fi

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "[ERROR] package.json not found! Make sure you're in the correct directory."
    exit 1
fi

# Start the development server in background
echo "[INFO] Starting development server in background..."
npm run dev &
SERVER_PID=$!

# Wait for server to start
echo "[INFO] Waiting for server to initialize..."
echo "[INFO] This usually takes 5-10 seconds..."
sleep 8

# Check if server is running
echo "[INFO] Checking if server is ready..."
if curl -s http://localhost:8080 > /dev/null 2>&1; then
    echo "[SUCCESS] Server is ready!"
else
    echo "[WARNING] Server may still be starting..."
fi

# Open browser
echo "[INFO] Opening browser..."
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:8080
elif command -v open > /dev/null; then
    open http://localhost:8080
else
    echo "[INFO] Please open http://localhost:8080 in your browser"
fi

echo ""
echo "========================================"
echo "[SUCCESS] Development server is running!"
echo "URL: http://localhost:8080"
echo ""
echo "To stop the server:"
echo "Press Ctrl+C in this terminal"
echo "========================================"
echo ""

# Wait for the server process
wait $SERVER_PID
