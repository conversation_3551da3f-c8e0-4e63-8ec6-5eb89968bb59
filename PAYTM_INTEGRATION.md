# Paytm Payment Gateway Integration

This document provides comprehensive information about the Paytm payment gateway integration with UPI support in the SoloGrind fitness application.

## Overview

The integration uses Paytm's JS Checkout solution, which is ideal for Capacitor-based hybrid applications. It supports multiple payment methods including UPI, Credit Cards, Debit Cards, Net Banking, and Paytm Wallet.

## Features

- ✅ **UPI Payment Support** - Pay using any UPI app (GPay, PhonePe, Paytm, BHIM, etc.)
- ✅ **Multiple Payment Methods** - Credit/Debit Cards, Net Banking, Paytm Wallet
- ✅ **Secure Transactions** - Bank-level security with checksum validation
- ✅ **Real-time Status** - Instant payment verification and wallet updates
- ✅ **Mobile Optimized** - Works seamlessly in Capacitor mobile apps
- ✅ **Environment Support** - Separate staging and production configurations
- ✅ **Webhook Integration** - Automatic payment status updates
- ✅ **Transaction History** - Complete audit trail of all payments

## Architecture

### Frontend Components
- `usePaytm` - Custom React hook for payment operations
- `PaymentMethodSelector` - UI for selecting payment methods
- `AddMoneyDialog` - Modal for initiating payments
- `PaymentStatus` - Real-time payment status tracking
- `Payment` - Main payment page
- `PaymentCallback` - Handles payment gateway redirects

### Backend Components (Supabase Edge Functions)
- `paytm-initiate` - Initiates payment transactions with checksum generation
- `paytm-verify` - Verifies payment status and updates database
- `paytm-webhook` - Handles payment status webhooks from Paytm

### Database Schema
- `payment_transactions` - Main payment records
- `paytm_transactions` - Paytm-specific transaction data
- `payment_methods` - Saved payment methods (future use)
- `payment_webhooks` - Webhook event logs

## Setup Instructions

### 1. Paytm Merchant Account Setup

1. **Create Paytm Merchant Account**
   - Visit [Paytm Business](https://business.paytm.com/)
   - Complete merchant onboarding process
   - Get your Merchant ID and Merchant Key

2. **Configure Staging Environment**
   - Use staging credentials for development
   - Download staging Paytm app for testing

### 2. Environment Configuration

1. **Copy environment template**
   ```bash
   cp .env.example .env.local
   ```

2. **Configure Paytm settings in `.env.local`**
   ```env
   # Paytm Configuration
   VITE_PAYTM_MERCHANT_ID=your_merchant_id
   VITE_PAYTM_STAGING=true
   VITE_PAYTM_CALLBACK_URL=http://localhost:8080/payment/callback
   VITE_PAYTM_WEBSITE=WEBSTAGING
   VITE_PAYTM_INDUSTRY_TYPE=Retail
   VITE_PAYTM_CHANNEL_ID=WEB
   ```

3. **Configure Supabase Edge Functions**
   Set these environment variables in your Supabase project:
   ```env
   PAYTM_MERCHANT_ID=your_merchant_id
   PAYTM_MERCHANT_KEY=your_merchant_key_secret
   PAYTM_STAGING=true
   PAYTM_CALLBACK_URL=https://your-domain.com/payment/callback
   ```

### 3. Database Setup

1. **Run the migration**
   ```bash
   supabase db push
   ```

2. **Deploy Edge Functions**
   ```bash
   supabase functions deploy paytm-initiate
   supabase functions deploy paytm-verify
   supabase functions deploy paytm-webhook
   ```

### 4. Testing

1. **Install staging Paytm app** on your test device
2. **Use test credentials** provided by Paytm
3. **Test UPI payments** with staging UPI IDs
4. **Verify webhook delivery** in Supabase logs

## Usage

### Basic Payment Flow

```typescript
import { usePaytm } from '@/hooks/usePaytm';

function PaymentComponent() {
  const { startPayment, openCheckout, verifyPayment } = usePaytm({
    onSuccess: (response) => {
      console.log('Payment successful:', response);
    },
    onFailure: (error) => {
      console.error('Payment failed:', error);
    }
  });

  const handlePayment = async () => {
    // 1. Initiate payment
    await startPayment({
      orderId: 'ORDER_123',
      amount: '100.00',
      customerId: 'user_id',
      customerEmail: '<EMAIL>',
      paymentModes: ['UPI', 'CC', 'DC']
    });

    // 2. Open checkout
    await openCheckout();
  };

  return (
    <button onClick={handlePayment}>
      Pay Now
    </button>
  );
}
```

### Add Money to Wallet

```typescript
import { AddMoneyDialog } from '@/components/payments/AddMoneyDialog';

function WalletComponent() {
  return (
    <AddMoneyDialog onSuccess={() => console.log('Money added!')}>
      <button>Add Money</button>
    </AddMoneyDialog>
  );
}
```

## Security Considerations

### Checksum Validation
- All transactions use SHA-256 checksum validation
- Merchant key is never exposed to frontend
- Server-side verification for all payments

### Environment Separation
- Separate staging and production configurations
- Different merchant IDs for each environment
- Secure environment variable management

### Data Protection
- All sensitive data encrypted in database
- PCI DSS compliant payment processing
- Secure webhook validation

## UPI Integration Details

### Supported UPI Apps
- Google Pay (GPay)
- PhonePe
- Paytm
- BHIM
- Amazon Pay
- Any UPI-enabled banking app

### UPI Flow
1. User selects UPI as payment method
2. Paytm JS Checkout opens UPI app selection
3. User completes payment in chosen UPI app
4. App redirects back to SoloGrind
5. Payment status verified and wallet updated

### Mobile App Considerations
- Proper URL scheme configuration for app returns
- Handle UPI app not installed scenarios
- Fallback to web-based UPI payment

## Troubleshooting

### Common Issues

1. **Checksum Mismatch**
   - Verify merchant key configuration
   - Check parameter ordering in checksum generation
   - Ensure no extra spaces in parameters

2. **Payment Gateway Not Loading**
   - Check Paytm script loading
   - Verify merchant ID configuration
   - Check network connectivity

3. **UPI App Not Opening**
   - Verify URL scheme configuration
   - Check if UPI app is installed
   - Test with different UPI apps

4. **Webhook Not Received**
   - Check webhook URL configuration
   - Verify Supabase Edge Function deployment
   - Check function logs for errors

### Debug Mode

Enable debug mode in development:
```env
VITE_DEBUG_MODE=true
```

This will show additional logging and debug information.

## Production Deployment

### Pre-deployment Checklist

- [ ] Update environment variables to production values
- [ ] Set `VITE_PAYTM_STAGING=false`
- [ ] Configure production callback URLs
- [ ] Test with production Paytm credentials
- [ ] Verify webhook endpoints are accessible
- [ ] Enable SSL/HTTPS for all endpoints
- [ ] Test payment flows end-to-end

### Monitoring

- Monitor Supabase Edge Function logs
- Track payment success/failure rates
- Set up alerts for webhook failures
- Monitor database performance

## Support

For technical issues:
1. Check Supabase function logs
2. Review payment webhook logs
3. Contact Paytm merchant support
4. Check Paytm developer documentation

## API Reference

### Paytm APIs Used
- **Initiate Transaction API** - Creates payment token
- **Transaction Status API** - Verifies payment status
- **Webhook API** - Receives payment notifications

### Custom APIs
- **POST** `/functions/v1/paytm-initiate` - Initiate payment
- **POST** `/functions/v1/paytm-verify` - Verify payment
- **POST** `/functions/v1/paytm-webhook` - Handle webhooks

## License

This integration is part of the SoloGrind application and follows the same license terms.
