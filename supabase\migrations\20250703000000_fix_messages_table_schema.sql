-- Fix messages table schema by adding missing columns
-- This migration adds the channel_type and guild_id columns that are expected by the chat functionality

-- Add channel_type column with default value 'global'
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS channel_type TEXT NOT NULL DEFAULT 'global';

-- Add guild_id column as nullable for future guild-specific messages
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS guild_id UUID REFERENCES public.guilds(id) ON DELETE SET NULL;

-- Add check constraint for channel_type
ALTER TABLE public.messages 
ADD CONSTRAINT IF NOT EXISTS messages_channel_type_check 
CHECK (channel_type IN ('global', 'guild', 'direct'));

-- Update existing messages to have channel_type = 'global' (in case any exist without this value)
UPDATE public.messages 
SET channel_type = 'global' 
WHERE channel_type IS NULL OR channel_type = '';

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_messages_channel_type ON public.messages(channel_type);
CREATE INDEX IF NOT EXISTS idx_messages_guild_id ON public.messages(guild_id) WHERE guild_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at);

-- Update RLS policies to handle the new columns
-- Drop existing policies first
DROP POLICY IF EXISTS "Public_messages_are_viewable_by_everyone" ON public.messages;
DROP POLICY IF EXISTS "Users_can_insert_their_own_messages" ON public.messages;

-- Create new RLS policies that handle different channel types
-- Global messages are viewable by everyone
CREATE POLICY "Global_messages_are_viewable_by_everyone" 
ON public.messages FOR SELECT 
USING (channel_type = 'global');

-- Guild messages are only viewable by guild members
CREATE POLICY "Guild_messages_are_viewable_by_members" 
ON public.messages FOR SELECT 
USING (
    channel_type = 'guild' AND 
    guild_id IS NOT NULL AND
    EXISTS (
        SELECT 1 
        FROM public.guild_members 
        WHERE guild_members.guild_id = messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can insert global messages
CREATE POLICY "Users_can_insert_global_messages" 
ON public.messages FOR INSERT 
WITH CHECK (
    auth.uid() = user_id AND 
    channel_type = 'global' AND 
    guild_id IS NULL
);

-- Users can insert guild messages if they are members
CREATE POLICY "Users_can_insert_guild_messages" 
ON public.messages FOR INSERT 
WITH CHECK (
    auth.uid() = user_id AND 
    channel_type = 'guild' AND 
    guild_id IS NOT NULL AND
    EXISTS (
        SELECT 1 
        FROM public.guild_members 
        WHERE guild_members.guild_id = messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can update their own messages
CREATE POLICY "Users_can_update_their_own_messages" 
ON public.messages FOR UPDATE 
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Users can delete their own messages
CREATE POLICY "Users_can_delete_their_own_messages" 
ON public.messages FOR DELETE 
USING (auth.uid() = user_id);

-- Add comment for documentation
COMMENT ON COLUMN public.messages.channel_type IS 'Type of channel: global, guild, or direct';
COMMENT ON COLUMN public.messages.guild_id IS 'Guild ID for guild-specific messages, NULL for global messages';
