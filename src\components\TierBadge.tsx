
import React from "react";

const tierColors = {
  D: "from-gray-500 to-gray-700",
  C: "from-purple-400 to-purple-600",
  B: "from-blue-400 to-blue-600",
  A: "from-cyan-400 to-cyan-600",
  S: "from-yellow-400 to-pink-500",
};

export function TierBadge({ tier }: { tier: "D" | "C" | "B" | "A" | "S" }) {
  return (
    <span
      className={`tier-badge inline-block bg-gradient-to-r ${tierColors[tier]} text-xs font-bold uppercase`}
      style={{ border: "2px solid #00d4ff", marginLeft: 6, minWidth: 32, textAlign: "center" }}
    >
      {tier}
    </span>
  );
}
