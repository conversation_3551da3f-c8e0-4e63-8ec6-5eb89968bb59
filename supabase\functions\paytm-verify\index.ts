// Paytm Verify Transaction Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { verifyChecksum } from '../_shared/paytm-checksum.ts'

interface VerifyTransactionRequest {
  orderId: string;
  txnId?: string;
}

interface PaytmStatusResponse {
  ORDERID: string;
  TXNID: string;
  TXNAMOUNT: string;
  STATUS: string;
  RESPCODE: string;
  RESPMSG: string;
  PAYMENTMODE: string;
  BANKNAME?: string;
  BANKTXNID?: string;
  GATEWAYNAME?: string;
  MID: string;
  TXNDATE: string;
  CHECKSUMHASH: string;
  CURRENCY: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('Missing authorization header')
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      throw new Error('Invalid authentication token')
    }

    // Parse request body
    const { orderId, txnId }: VerifyTransactionRequest = await req.json()

    if (!orderId) {
      throw new Error('Order ID is required')
    }

    // Get Paytm configuration
    const PAYTM_MID = Deno.env.get('PAYTM_MERCHANT_ID')
    const PAYTM_KEY = Deno.env.get('PAYTM_MERCHANT_KEY')
    const PAYTM_STAGING = Deno.env.get('PAYTM_STAGING') === 'true'

    if (!PAYTM_MID || !PAYTM_KEY) {
      throw new Error('Paytm configuration is incomplete')
    }

    // Get payment transaction from database
    const { data: paymentTransaction, error: transactionError } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('order_id', orderId)
      .eq('user_id', user.id)
      .single()

    if (transactionError || !paymentTransaction) {
      throw new Error('Payment transaction not found')
    }

    // Prepare request body for Paytm status API
    const statusRequestBody = {
      body: {
        mid: PAYTM_MID,
        orderId: orderId,
      },
      head: {
        signature: await generateChecksum({ MID: PAYTM_MID, ORDERID: orderId }, PAYTM_KEY),
      },
    }

    // Call Paytm Transaction Status API
    const paytmStatusUrl = PAYTM_STAGING 
      ? 'https://securestage.paytmpayments.com/v3/order/status'
      : 'https://secure.paytmpayments.com/v3/order/status'

    const statusResponse = await fetch(paytmStatusUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(statusRequestBody),
    })

    const statusData = await statusResponse.json()

    if (!statusResponse.ok) {
      throw new Error(`Paytm status API error: ${statusData.body?.resultInfo?.resultMsg || 'Unknown error'}`)
    }

    const transactionStatus = statusData.body

    // Verify checksum if present
    if (transactionStatus.CHECKSUMHASH) {
      const isValidChecksum = await verifyChecksum(transactionStatus, PAYTM_KEY, transactionStatus.CHECKSUMHASH)
      if (!isValidChecksum) {
        throw new Error('Invalid checksum in transaction response')
      }
    }

    // Determine transaction status
    let finalStatus = 'pending'
    if (transactionStatus.STATUS === 'TXN_SUCCESS') {
      finalStatus = 'success'
    } else if (transactionStatus.STATUS === 'TXN_FAILURE') {
      finalStatus = 'failed'
    } else if (transactionStatus.STATUS === 'PENDING') {
      finalStatus = 'pending'
    }

    // Update payment transaction
    const { error: updateError } = await supabaseClient
      .from('payment_transactions')
      .update({
        status: finalStatus,
        transaction_id: transactionStatus.TXNID,
        gateway_response: transactionStatus,
        updated_at: new Date().toISOString(),
        ...(finalStatus === 'success' && { completed_at: new Date().toISOString() })
      })
      .eq('id', paymentTransaction.id)

    if (updateError) {
      console.error('Error updating payment transaction:', updateError)
    }

    // Update Paytm transaction record
    const { error: paytmUpdateError } = await supabaseClient
      .from('paytm_transactions')
      .update({
        txn_id: transactionStatus.TXNID,
        bank_txn_id: transactionStatus.BANKTXNID,
        bank_name: transactionStatus.BANKNAME,
        gateway_name: transactionStatus.GATEWAYNAME,
        payment_mode: transactionStatus.PAYMENTMODE,
        resp_code: transactionStatus.RESPCODE,
        resp_msg: transactionStatus.RESPMSG,
        checksum_hash: transactionStatus.CHECKSUMHASH,
        txn_date: transactionStatus.TXNDATE ? new Date(transactionStatus.TXNDATE).toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('payment_transaction_id', paymentTransaction.id)

    if (paytmUpdateError) {
      console.error('Error updating Paytm transaction:', paytmUpdateError)
    }

    // Return verification result
    return new Response(
      JSON.stringify({
        success: true,
        orderId: orderId,
        txnId: transactionStatus.TXNID,
        status: finalStatus,
        amount: transactionStatus.TXNAMOUNT,
        paymentMode: transactionStatus.PAYMENTMODE,
        respCode: transactionStatus.RESPCODE,
        respMsg: transactionStatus.RESPMSG,
        txnDate: transactionStatus.TXNDATE,
        isVerified: true,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in paytm-verify function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

// Helper function to generate checksum (simplified version)
async function generateChecksum(params: Record<string, string>, key: string): Promise<string> {
  // This is a simplified implementation
  // In production, use the official Paytm checksum library
  const sortedParams = Object.keys(params)
    .sort()
    .map(k => `${k}=${params[k]}`)
    .join('&')
  
  const data = sortedParams + key
  const encoder = new TextEncoder()
  const dataBuffer = encoder.encode(data)
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}
