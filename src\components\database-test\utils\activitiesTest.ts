// User activities table testing utilities
import { supabase } from '@/integrations/supabase/client';
import { TestResult } from '../types';

export const testUserActivities = async (
  userId: string,
  addResult: (result: Omit<TestResult, 'timestamp'>) => void
) => {
  const startTime = Date.now();
  try {
    // Test READ
    const { data: activities, error: readError } = await supabase
      .from('user_activities')
      .select('*')
      .eq('user_id', userId)
      .limit(5);
    
    if (readError) {
      addResult({
        table: 'user_activities',
        operation: 'READ',
        status: 'error',
        message: readError.message,
        duration: Date.now() - startTime,
      });
      return;
    }
    
    addResult({
      table: 'user_activities',
      operation: 'READ',
      status: 'success',
      message: `Found ${activities?.length || 0} activities`,
      duration: Date.now() - startTime,
    });

    // Test CREATE (insert test activity)
    const createStartTime = Date.now();
    const testActivity = {
      user_id: userId,
      distance_km: 0.1,
      activity_type: 'Test',
      activity_date: new Date().toISOString().split('T')[0],
    };
    
    const { data: newActivity, error: createError } = await supabase
      .from('user_activities')
      .insert(testActivity)
      .select()
      .single();
    
    if (createError) {
      addResult({
        table: 'user_activities',
        operation: 'CREATE',
        status: 'error',
        message: createError.message,
        duration: Date.now() - createStartTime,
      });
      return;
    }
    
    addResult({
      table: 'user_activities',
      operation: 'CREATE',
      status: 'success',
      message: 'Test activity created',
      duration: Date.now() - createStartTime,
    });

    // Test DELETE (remove test activity)
    if (newActivity) {
      const deleteStartTime = Date.now();
      const { error: deleteError } = await supabase
        .from('user_activities')
        .delete()
        .eq('id', newActivity.id);
      
      if (deleteError) {
        addResult({
          table: 'user_activities',
          operation: 'DELETE',
          status: 'error',
          message: deleteError.message,
          duration: Date.now() - deleteStartTime,
        });
      } else {
        addResult({
          table: 'user_activities',
          operation: 'DELETE',
          status: 'success',
          message: 'Test activity deleted',
          duration: Date.now() - deleteStartTime,
        });
      }
    }
  } catch (error) {
    addResult({
      table: 'user_activities',
      operation: 'CRUD',
      status: 'error',
      message: `Unexpected error: ${error}`,
      duration: Date.now() - startTime,
    });
  }
};
