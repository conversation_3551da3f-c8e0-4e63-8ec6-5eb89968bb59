// Recent logs panel
import { Bug } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LogEntry } from './types';

interface LogsPanelProps {
  recentLogs: LogEntry[];
}

export function LogsPanel({ recentLogs }: LogsPanelProps) {
  const getLogColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error':
        return 'text-red-400';
      case 'warn':
        return 'text-yellow-400';
      case 'info':
        return 'text-blue-400';
      case 'debug':
        return 'text-gray-400';
      default:
        return 'text-white/60';
    }
  };

  return (
    <Card className="bg-black/40 border-white/10">
      <CardHeader className="pb-2">
        <CardTitle className="text-xs text-white/80 flex items-center gap-1">
          <Bug className="h-3 w-3" />
          Recent Logs ({recentLogs.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1">
        {recentLogs.length === 0 ? (
          <div className="text-xs text-white/40">No recent logs</div>
        ) : (
          recentLogs.map((log, index) => (
            <div key={index} className="text-xs">
              <div className="flex items-center gap-1">
                <span className="text-white/40">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
                <span className={getLogColor(log.level)}>
                  {log.level.toUpperCase()}
                </span>
              </div>
              <div className="text-white/60 truncate">
                {log.message}
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
