import React, { Component, ErrorInfo, ReactNode } from 'react';
import { GlassCard } from './GlassCard';
import { Button } from './ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { logReactError } from '@/lib/logger';
import { trackError } from '@/lib/analytics';
import { isDevelopment } from '@/lib/config';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ error, errorInfo });
    
    // Log the error
    logReactError(error, errorInfo);
    
    // Track the error in analytics
    trackError(error.message, {
      component_stack: errorInfo.componentStack,
      error_stack: error.stack,
      error_name: error.name,
    });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <>
          <div
            style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
            className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
          />
          <div className="relative z-10 min-h-screen bg-black/70 flex items-center justify-center p-4">
            <GlassCard className="max-w-md w-full p-6 text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-red-500" />
              </div>
              
              <h1 className="text-2xl font-bold text-white mb-2">
                Oops! Something went wrong
              </h1>
              
              <p className="text-white/70 mb-6">
                We encountered an unexpected error. Don't worry, your data is safe.
              </p>

              {isDevelopment() && this.state.error && (
                <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded text-left">
                  <h3 className="text-red-400 font-semibold mb-2">Error Details (Development)</h3>
                  <p className="text-red-300 text-sm font-mono mb-2">
                    {this.state.error.name}: {this.state.error.message}
                  </p>
                  {this.state.error.stack && (
                    <details className="text-red-300 text-xs">
                      <summary className="cursor-pointer">Stack Trace</summary>
                      <pre className="mt-2 whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <details className="text-red-300 text-xs mt-2">
                      <summary className="cursor-pointer">Component Stack</summary>
                      <pre className="mt-2 whitespace-pre-wrap">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              )}

              <div className="space-y-3">
                <Button
                  onClick={this.handleReset}
                  className="w-full bg-electric hover:bg-purple text-white"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="w-full text-white border-white/20"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              <p className="text-white/50 text-xs mt-4">
                If this problem persists, please contact support.
              </p>
            </GlassCard>
          </div>
        </>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: any) => {
    logReactError(error, errorInfo || {});
    trackError(error.message, {
      error_stack: error.stack,
      error_name: error.name,
      ...errorInfo,
    });
  };

  return { handleError };
};
