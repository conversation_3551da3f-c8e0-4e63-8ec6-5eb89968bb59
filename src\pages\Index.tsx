// Re-export the refactored dashboard components
// This maintains backward compatibility while using the new modular structure
import { Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { AuthenticatedDashboard, GuestLanding } from "@/components/dashboard";

export default function Index() {
  const { user, loading } = useAuth();

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70 flex flex-col">
        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 text-electric animate-spin" />
          </div>
        ) : user ? (
          <AuthenticatedDashboard />
        ) : (
          <GuestLanding />
        )}
      </div>
    </>
  );
}
