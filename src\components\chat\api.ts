import { supabase } from '@/integrations/supabase/client';
import { MessageWithProfile, GuildMessageWithProfile } from './types';

// Fetch global chat messages
export const fetchGlobalMessages = async (): Promise<MessageWithProfile[]> => {
  const { data, error } = await supabase
    .from('messages')
    .select('*, profiles(username, avatar_url)')
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching global messages:', error);
    throw new Error(error.message);
  }
  return data as MessageWithProfile[];
};

// Fetch guild chat messages
export const fetchGuildMessages = async (guildId: string): Promise<GuildMessageWithProfile[]> => {
  const { data, error } = await supabase
    .from('guild_messages')
    .select('*, profiles(username, avatar_url)')
    .eq('guild_id', guildId)
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching guild messages:', error);
    throw new Error(error.message);
  }
  return data as GuildMessageWithProfile[];
};

// Send global message
export const sendGlobalMessage = async (content: string, userId: string): Promise<void> => {
  const { error } = await supabase.from('messages').insert({
    content,
    user_id: userId,
  });
  
  if (error) {
    console.error("Error sending global message:", error);
    throw error;
  }
};

// Send guild message
export const sendGuildMessage = async (content: string, userId: string, guildId: string): Promise<void> => {
  const { error } = await supabase.from('guild_messages').insert({
    content,
    user_id: userId,
    guild_id: guildId,
  });
  
  if (error) {
    console.error("Error sending guild message:", error);
    throw error;
  }
};
