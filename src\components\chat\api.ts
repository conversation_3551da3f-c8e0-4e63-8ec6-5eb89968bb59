import { supabase } from '@/integrations/supabase/client';
import { MessageWithProfile, GuildMessageWithProfile } from './types';

// Fetch global chat messages
export const fetchGlobalMessages = async (): Promise<MessageWithProfile[]> => {
  console.log('Fetching global messages...');

  const { data, error } = await supabase
    .from('messages')
    .select('*, profiles(username, avatar_url)')
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching global messages:', error);
    throw new Error(error.message);
  }

  console.log('Global messages fetched:', data?.length || 0, 'messages');
  return data as MessageWithProfile[];
};

// Fetch guild chat messages
export const fetchGuildMessages = async (guildId: string): Promise<GuildMessageWithProfile[]> => {
  console.log('Fetching guild messages for guild ID:', guildId);

  // First, get the guild messages without profiles
  const { data: messages, error: messagesError } = await supabase
    .from('guild_messages')
    .select('*')
    .eq('guild_id', guildId)
    .order('created_at', { ascending: true })
    .limit(100);

  if (messagesError) {
    console.error('Error fetching guild messages:', messagesError);
    throw new Error(messagesError.message);
  }

  if (!messages || messages.length === 0) {
    console.log('No guild messages found');
    return [];
  }

  // Get unique user IDs from messages
  const userIds = [...new Set(messages.map(msg => msg.user_id))];

  // Fetch profiles for these users
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('id, username, avatar_url')
    .in('id', userIds);

  if (profilesError) {
    console.error('Error fetching profiles for guild messages:', profilesError);
    // Continue without profiles rather than failing completely
  }

  // Combine messages with profiles
  const messagesWithProfiles = messages.map(message => ({
    ...message,
    profiles: profiles?.find(profile => profile.id === message.user_id) || null
  }));

  console.log('Guild messages fetched:', messagesWithProfiles.length, 'messages');
  return messagesWithProfiles as GuildMessageWithProfile[];
};

// Send global message
export const sendGlobalMessage = async (content: string, userId: string): Promise<void> => {
  const { error } = await supabase.from('messages').insert({
    content,
    user_id: userId,
  });
  
  if (error) {
    console.error("Error sending global message:", error);
    throw error;
  }
};

// Send guild message
export const sendGuildMessage = async (content: string, userId: string, guildId: string): Promise<void> => {
  const { error } = await supabase.from('guild_messages').insert({
    content,
    user_id: userId,
    guild_id: guildId,
  });
  
  if (error) {
    console.error("Error sending guild message:", error);
    throw error;
  }
};
