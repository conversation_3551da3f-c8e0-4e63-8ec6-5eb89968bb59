
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { Tables } from "@/integrations/supabase/types";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

export type GuildData = Tables<'guild_members'> & { guilds: Tables<'guilds'> | null };

export type Member = Tables<'guild_members'> & {
  profiles: Pick<Tables<'profiles'>, 'id' | 'username' | 'avatar_url'> | null;
  total_distance: number | null;
};

// Fetch user's guild
const fetchUserGuild = async (userId: string) => {
  const { data, error } = await supabase
    .from("guild_members")
    .select("*, guilds(*)")
    .eq("user_id", userId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116: no rows found
    throw new Error(error.message);
  }
  return data;
};

// Fetch guild members
const fetchGuildMembers = async (guildId: string): Promise<Member[]> => {
    const { data: membersData, error: membersError } = await supabase
        .from('guild_members')
        .select('*, profiles:user_id(id, username, avatar_url)')
        .eq('guild_id', guildId);

    if (membersError) throw new Error(membersError.message);
    if (!membersData) return [];

    const memberUserIds = membersData.map(m => m.user_id);

    const { data: statsData, error: statsError } = await supabase
        .from('user_leaderboard_stats')
        .select('user_id, total_distance')
        .in('user_id', memberUserIds);

    if (statsError) throw new Error(statsError.message);

    const membersWithStats = membersData.map((member) => {
        const stats = statsData?.find(s => s.user_id === member.user_id);
        return {
            ...(member as any), // Type assertion to combine
            total_distance: stats?.total_distance || 0,
        };
    });

    // Sort by total_distance descending
    return membersWithStats.sort((a, b) => (b.total_distance || 0) - (a.total_distance || 0));
};

export const useGuild = () => {
    const { user } = useAuth();
    const queryClient = useQueryClient();
    const { toast } = useToast();

    const { data: userGuild, isLoading: isLoadingUserGuild, error: userGuildError } = useQuery({
        queryKey: ['userGuild', user?.id],
        queryFn: () => fetchUserGuild(user!.id),
        enabled: !!user,
    });

    const guildId = userGuild?.guild_id;
    const guild = userGuild?.guilds;

    const { data: members, isLoading: isLoadingMembers, error: membersError } = useQuery({
        queryKey: ['guildMembers', guildId],
        queryFn: () => fetchGuildMembers(guildId!),
        enabled: !!guildId,
    });
    
    const leaveGuildMutation = useMutation({
        mutationFn: async () => {
            if (!guildId || !user) throw new Error("Not in a guild or not logged in.");
            const { error } = await supabase.from('guild_members').delete().match({ guild_id: guildId, user_id: user.id });
            if (error) throw error;
        },
        onSuccess: () => {
            toast({
                title: "Success",
                description: "You have left the guild.",
            });
            queryClient.invalidateQueries({ queryKey: ['userGuild', user?.id] });
            queryClient.invalidateQueries({ queryKey: ['guildMembers', guildId] });
        },
        onError: (error: Error) => {
            toast({
                variant: "destructive",
                title: "Error leaving guild",
                description: error.message,
            });
        },
    });

    return {
        userGuild,
        guild,
        guildId,
        members,
        isLoadingUserGuild,
        isLoadingMembers,
        userGuildError,
        membersError,
        leaveGuild: leaveGuildMutation,
    };
};
