import { Footprints, Timer } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { formatDuration } from './utils/formatters';

interface SessionMetricsProps {
  distance: number;
  duration: number;
  isTracking: boolean;
}

export function SessionMetrics({ distance, duration, isTracking }: SessionMetricsProps) {
  return (
    <div className="flex flex-col items-center justify-center mb-6">
      <div className="w-full max-w-sm space-y-4">
        <GlassCard className="p-6 flex flex-col items-center">
          <Footprints className="text-electric mb-2" size={32} />
          <span className="text-sm text-white/70">DISTANCE</span>
          <span className="text-4xl font-bold text-white">{distance.toFixed(2)} km</span>
          {isTracking && (
            <div className="flex items-center gap-1 mt-2">
              <div className="w-2 h-2 bg-electric rounded-full animate-pulse" />
              <span className="text-xs text-electric">Recording...</span>
            </div>
          )}
        </GlassCard>

        <GlassCard className="p-6 flex flex-col items-center">
          <Timer className="text-electric mb-2" size={32} />
          <span className="text-sm text-white/70">TIME</span>
          <span className="text-4xl font-bold text-white">{formatDuration(duration)}</span>
          {isTracking && (
            <div className="flex items-center gap-1 mt-2">
              <div className="w-2 h-2 bg-electric rounded-full animate-pulse" />
              <span className="text-xs text-electric">Running...</span>
            </div>
          )}
        </GlassCard>
      </div>
    </div>
  );
}
