// Profiles table testing utilities
import { supabase } from '@/integrations/supabase/client';
import { TestResult } from '../types';

export const testProfiles = async (
  userId: string,
  addResult: (result: Omit<TestResult, 'timestamp'>) => void
) => {
  const startTime = Date.now();
  try {
    // Test READ
    const { data: profile, error: readError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (readError) {
      addResult({
        table: 'profiles',
        operation: 'READ',
        status: 'error',
        message: readError.message,
        duration: Date.now() - startTime,
      });
      return;
    }
    
    addResult({
      table: 'profiles',
      operation: 'READ',
      status: 'success',
      message: `Profile loaded: ${profile?.username || 'No username'}`,
      duration: Date.now() - startTime,
    });

    // Test UPDATE (if profile exists)
    if (profile) {
      const updateStartTime = Date.now();
      const testUpdate = { updated_at: new Date().toISOString() };
      
      const { error: updateError } = await supabase
        .from('profiles')
        .update(testUpdate)
        .eq('id', userId);
      
      if (updateError) {
        addResult({
          table: 'profiles',
          operation: 'UPDATE',
          status: 'error',
          message: updateError.message,
          duration: Date.now() - updateStartTime,
        });
      } else {
        addResult({
          table: 'profiles',
          operation: 'UPDATE',
          status: 'success',
          message: 'Profile updated successfully',
          duration: Date.now() - updateStartTime,
        });
      }
    }
  } catch (error) {
    addResult({
      table: 'profiles',
      operation: 'READ',
      status: 'error',
      message: `Unexpected error: ${error}`,
      duration: Date.now() - startTime,
    });
  }
};
