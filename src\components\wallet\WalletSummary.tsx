
import { GlassCard } from "../GlassCard";
import { WithdrawalDrawer } from "./WithdrawalDrawer";
import { AddMoneyDialog } from "../payments/AddMoneyDialog";
import { Tables } from "@/integrations/supabase/types";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";
import { Zap, Plus } from "lucide-react";
import { isPaytmEnabled } from "@/lib/config";

interface WalletSummaryProps {
  wallet: Tables<'wallets'>;
}

export function WalletSummary({ wallet }: WalletSummaryProps) {
  const { isSubscribed, isTrialActive } = useAuth();

  return (
    <GlassCard className="p-6 flex flex-col items-center justify-center text-center">
      <p className="text-white/70 text-sm">Current Balance</p>
      <h2 className="text-4xl font-bold text-electric tracking-tight my-2">
        ${wallet.balance.toFixed(2)}
      </h2>
      <div className="mt-4 flex gap-3">
        {isPaytmEnabled() && (
          <AddMoneyDialog>
            <button className="flex-1 bg-electric hover:bg-purple text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
              <Plus className="h-4 w-4" />
              Add Money
            </button>
          </AddMoneyDialog>
        )}
        <WithdrawalDrawer balance={wallet.balance} walletId={wallet.id} />
      </div>

      {!isSubscribed && (
        <div className="mt-6 border-t border-white/10 pt-4 w-full">
          <Link
            to="/subscription"
            className="inline-flex items-center gap-2 text-electric font-semibold hover:text-electric/80 transition-colors"
          >
            <Zap size={16} />
            Upgrade to Pro to Earn Cashback
          </Link>
          <p className="text-xs text-white/50 mt-1">
            {isTrialActive
              ? "Start earning real cash with every step."
              : "Your trial is over. Subscribe to unlock earnings!"}
          </p>
        </div>
      )}
    </GlassCard>
  );
}
