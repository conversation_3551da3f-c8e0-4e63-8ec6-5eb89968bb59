// Main sidebar exports - maintains backward compatibility
export { useSidebar } from "./context"
export { SidebarProvider } from "./SidebarProvider"
export { Sidebar } from "./Sidebar"
export { SidebarTrigger, SidebarRail, SidebarInset } from "./SidebarControls"
export { 
  SidebarInput, 
  SidebarHeader, 
  SidebarFooter, 
  SidebarSeparator, 
  SidebarContent, 
  SidebarGroup 
} from "./SidebarLayout"
export { 
  SidebarGroupLabel, 
  SidebarGroupAction, 
  SidebarGroupContent 
} from "./SidebarGroup"
export { SidebarMenu, SidebarMenuItem, SidebarMenuButton } from "./SidebarMenu"
export { 
  SidebarMenuAction, 
  SidebarMenuBadge, 
  SidebarMenuSkeleton 
} from "./SidebarMenuExtras"
export { 
  SidebarMenuSub, 
  SidebarMenuSubItem, 
  SidebarMenuSubButton 
} from "./SidebarMenuSub"

// Export types
export type { SidebarContext } from "./types"
