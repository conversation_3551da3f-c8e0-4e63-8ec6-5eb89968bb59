
-- Create a table for public chat messages
CREATE TABLE public.messages (
  id uuid NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- RLS policies for messages
CREATE POLICY "Public_messages_are_viewable_by_everyone" ON public.messages FOR SELECT USING (true);
CREATE POLICY "Users_can_insert_their_own_messages" ON public.messages FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Set up realtime
ALTER TABLE public.messages REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
