#!/bin/bash

echo "========================================"
echo "SoloGrind Project Status Checker"
echo "========================================"
echo

echo "[1/5] Checking Node.js and npm..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js first."
    exit 1
else
    echo "✅ Node.js: $(node --version)"
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm not found."
    exit 1
else
    echo "✅ npm: $(npm --version)"
fi

echo
echo "[2/5] Checking project dependencies..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  Dependencies not installed. Installing now..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies."
        exit 1
    fi
else
    echo "✅ Dependencies installed"
fi

echo
echo "[3/5] Checking environment configuration..."
if [ -f ".env.development" ]; then
    echo "✅ Development environment configured"
else
    echo "❌ Missing .env.development file"
fi

if [ -f ".env.production" ]; then
    echo "✅ Production environment configured"
else
    echo "❌ Missing .env.production file"
fi

echo
echo "[4/5] Checking TypeScript configuration..."
if [ -f "tsconfig.json" ]; then
    echo "✅ TypeScript configuration found"
else
    echo "❌ Missing TypeScript configuration"
fi

echo
echo "[5/5] Checking build status..."
echo "Testing TypeScript compilation..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
    echo "❌ TypeScript compilation errors found"
    echo "Please fix the errors before continuing."
    exit 1
else
    echo "✅ TypeScript compilation successful"
fi

echo
echo "========================================"
echo "🎉 PROJECT STATUS: READY FOR DEVELOPMENT"
echo "========================================"
echo
echo "Your SoloGrind project is properly configured and ready to run!"
echo
echo "To start development:"
echo "  1. Run: ./start-dev.sh (development server only)"
echo "  2. Run: ./start-browser.sh (server + auto-open browser)"
echo "  3. Manual: npm run dev"
echo
echo "Development URLs:"
echo "  • Main App: http://localhost:8080"
echo "  • Analytics Test: http://localhost:8080/tracking-test"
echo "  • Database Test: http://localhost:8080/database-test"
echo
echo "========================================"
