import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export function useUserStats() {
  const { user } = useAuth();

  // User leaderboard stats
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['user-leaderboard-stats-home', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_leaderboard_stats')
        .select('total_distance')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user stats for home:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  // User settings
  const { data: userSettings, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['user-settings', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_settings')
        .select('daily_goal_km, weekly_goal_km')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user settings:', error);
        return { daily_goal_km: 6, weekly_goal_km: 42 }; // Default values
      }
      return data;
    },
    enabled: !!user,
  });

  // Guild membership
  const { data: guildMembership } = useQuery({
    queryKey: ['guild-membership', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('guild_members')
        .select(`
          guild_id,
          guilds (
            name,
            icon_name
          )
        `)
        .eq('user_id', user.id)
        .eq('status', 'accepted')
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching guild membership:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  // Guild rank
  const { data: guildRank } = useQuery({
    queryKey: ['guild-rank', user?.id, guildMembership?.guild_id],
    queryFn: async () => {
      if (!user || !guildMembership?.guild_id) return null;
      
      const { data, error } = await supabase
        .from('guild_leaderboard')
        .select('rank')
        .eq('user_id', user.id)
        .eq('guild_id', guildMembership.guild_id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching guild rank:', error);
        return null;
      }
      return data?.rank;
    },
    enabled: !!user && !!guildMembership?.guild_id,
  });

  return {
    stats,
    isLoadingStats,
    userSettings,
    isLoadingSettings,
    guildMembership,
    guildRank,
  };
}
