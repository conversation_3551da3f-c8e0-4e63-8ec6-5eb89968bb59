// Database test controls component
import { Database, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { GlassCard } from '@/components/GlassCard';

interface TestControlsProps {
  onRunTests: () => void;
  isRunningTests: boolean;
  hasUser: boolean;
}

export function TestControls({ onRunTests, isRunningTests, hasUser }: TestControlsProps) {
  return (
    <GlassCard className="p-6">
      <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
        <Database className="h-5 w-5" />
        Database Tests
      </h2>
      <Button
        onClick={onRunTests}
        disabled={isRunningTests || !hasUser}
        className="w-full bg-electric hover:bg-purple text-white"
      >
        {isRunningTests ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Running Tests...
          </>
        ) : (
          'Run Database Tests'
        )}
      </Button>
      {!hasUser && (
        <p className="text-white/60 text-sm mt-2 text-center">
          Please log in to run database tests
        </p>
      )}
    </GlassCard>
  );
}
