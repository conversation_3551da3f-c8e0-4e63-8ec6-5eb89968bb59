-- Migration: Add Payment Tables for Paytm Integration
-- Created: 2025-06-26
-- Description: Creates tables for payment transactions, Paytm-specific data, and payment methods

-- Create payment_transactions table
CREATE TABLE public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE CASCADE,
    order_id VARCHAR(100) UNIQUE NOT NULL,
    amount NUMERIC(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'INR',
    payment_method VARCHAR(20) NOT NULL, -- UPI, CC, DC, NB, WALLET
    payment_provider VARCHAR(20) NOT NULL DEFAULT 'paytm',
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, success, failed, cancelled
    transaction_id VARCHAR(100), -- Provider transaction ID
    provider_order_id VARCHAR(100), -- Provider's order ID
    gateway_response JSONB, -- Full response from payment gateway
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    completed_at TIMESTAMPTZ
);

COMMENT ON TABLE public.payment_transactions IS 'Stores all payment transactions across different providers';
COMMENT ON COLUMN public.payment_transactions.payment_method IS 'Payment method used: UPI, CC (Credit Card), DC (Debit Card), NB (Net Banking), WALLET';
COMMENT ON COLUMN public.payment_transactions.status IS 'Transaction status: pending, success, failed, cancelled';
COMMENT ON COLUMN public.payment_transactions.gateway_response IS 'Full JSON response from payment gateway for debugging and reconciliation';

-- Create paytm_transactions table for Paytm-specific data
CREATE TABLE public.paytm_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_transaction_id UUID NOT NULL REFERENCES public.payment_transactions(id) ON DELETE CASCADE,
    merchant_id VARCHAR(50) NOT NULL,
    txn_token TEXT, -- Transaction token from Paytm
    txn_id VARCHAR(100), -- Paytm transaction ID
    bank_txn_id VARCHAR(100), -- Bank transaction ID
    bank_name VARCHAR(100), -- Bank name
    gateway_name VARCHAR(100), -- Gateway name
    payment_mode VARCHAR(20), -- UPI, CC, DC, NB, WALLET, EMI
    resp_code VARCHAR(10), -- Response code from Paytm
    resp_msg TEXT, -- Response message from Paytm
    checksum_hash TEXT, -- Checksum for verification
    txn_date TIMESTAMPTZ, -- Transaction date from Paytm
    refund_amount NUMERIC(10, 2) DEFAULT 0.00, -- Refunded amount if any
    is_refunded BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.paytm_transactions IS 'Stores Paytm-specific transaction data and responses';
COMMENT ON COLUMN public.paytm_transactions.txn_token IS 'Transaction token received from Paytm initiate API';
COMMENT ON COLUMN public.paytm_transactions.checksum_hash IS 'Checksum hash for transaction verification';

-- Create payment_methods table for saved payment methods
CREATE TABLE public.payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    method_type VARCHAR(20) NOT NULL, -- UPI, CARD, NETBANKING
    provider VARCHAR(50) NOT NULL, -- paytm, gpay, phonepe, etc.
    display_name VARCHAR(100) NOT NULL, -- User-friendly name
    method_details JSONB NOT NULL, -- Encrypted/tokenized payment method details
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.payment_methods IS 'Stores user saved payment methods (tokenized/encrypted)';
COMMENT ON COLUMN public.payment_methods.method_details IS 'Encrypted or tokenized payment method details';

-- Create payment_webhooks table for webhook logging
CREATE TABLE public.payment_webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_transaction_id UUID REFERENCES public.payment_transactions(id) ON DELETE SET NULL,
    provider VARCHAR(20) NOT NULL,
    webhook_type VARCHAR(50) NOT NULL, -- payment_success, payment_failed, refund, etc.
    payload JSONB NOT NULL,
    headers JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMPTZ,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.payment_webhooks IS 'Logs all payment webhook events for debugging and reconciliation';

-- Add indexes for better performance
CREATE INDEX idx_payment_transactions_user_id ON public.payment_transactions(user_id);
CREATE INDEX idx_payment_transactions_order_id ON public.payment_transactions(order_id);
CREATE INDEX idx_payment_transactions_status ON public.payment_transactions(status);
CREATE INDEX idx_payment_transactions_created_at ON public.payment_transactions(created_at);
CREATE INDEX idx_paytm_transactions_payment_id ON public.paytm_transactions(payment_transaction_id);
CREATE INDEX idx_paytm_transactions_txn_id ON public.paytm_transactions(txn_id);
CREATE INDEX idx_payment_methods_user_id ON public.payment_methods(user_id);
CREATE INDEX idx_payment_webhooks_transaction_id ON public.payment_webhooks(payment_transaction_id);
CREATE INDEX idx_payment_webhooks_processed ON public.payment_webhooks(processed);

-- Enable Row Level Security
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paytm_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_webhooks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for payment_transactions
CREATE POLICY "Users can view their own payment transactions" 
    ON public.payment_transactions FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payment transactions" 
    ON public.payment_transactions FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own payment transactions" 
    ON public.payment_transactions FOR UPDATE 
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all payment transactions" 
    ON public.payment_transactions FOR ALL 
    USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- RLS Policies for paytm_transactions
CREATE POLICY "Users can view their own paytm transactions" 
    ON public.paytm_transactions FOR SELECT 
    USING (EXISTS (SELECT 1 FROM public.payment_transactions WHERE payment_transactions.id = paytm_transactions.payment_transaction_id AND payment_transactions.user_id = auth.uid()));

CREATE POLICY "Service role can manage paytm transactions" 
    ON public.paytm_transactions FOR ALL 
    USING (auth.role() = 'service_role');

CREATE POLICY "Admins can manage all paytm transactions" 
    ON public.paytm_transactions FOR ALL 
    USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- RLS Policies for payment_methods
CREATE POLICY "Users can manage their own payment methods" 
    ON public.payment_methods FOR ALL 
    USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all payment methods" 
    ON public.payment_methods FOR ALL 
    USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- RLS Policies for payment_webhooks (service role only)
CREATE POLICY "Service role can manage payment webhooks" 
    ON public.payment_webhooks FOR ALL 
    USING (auth.role() = 'service_role');

CREATE POLICY "Admins can view payment webhooks" 
    ON public.payment_webhooks FOR SELECT 
    USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- Create function to update wallet balance after successful payment
CREATE OR REPLACE FUNCTION public.handle_successful_payment()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process if status changed to 'success'
    IF NEW.status = 'success' AND (OLD.status IS NULL OR OLD.status != 'success') THEN
        -- Update wallet balance
        UPDATE public.wallets 
        SET balance = balance + NEW.amount,
            updated_at = now()
        WHERE id = NEW.wallet_id;
        
        -- Insert wallet transaction record
        INSERT INTO public.wallet_transactions (
            wallet_id,
            transaction_type,
            amount,
            description,
            reference_id,
            created_at
        ) VALUES (
            NEW.wallet_id,
            'credit',
            NEW.amount,
            'Payment via ' || NEW.payment_method || ' - Order: ' || NEW.order_id,
            NEW.id,
            now()
        );
        
        -- Update completed_at timestamp
        NEW.completed_at = now();
    END IF;
    
    -- Always update updated_at
    NEW.updated_at = now();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for successful payments
CREATE TRIGGER trigger_handle_successful_payment
    BEFORE UPDATE ON public.payment_transactions
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_successful_payment();

-- Create function to generate order ID
CREATE OR REPLACE FUNCTION public.generate_order_id(prefix TEXT DEFAULT 'ORDER')
RETURNS TEXT AS $$
BEGIN
    RETURN prefix || '_' || EXTRACT(EPOCH FROM now())::BIGINT || '_' || FLOOR(RANDOM() * 1000)::INT;
END;
$$ LANGUAGE plpgsql;
