import * as React from "react"
import { SidebarContext as SidebarContextType } from "./types"

// Sidebar context for state management
export const SidebarContext = React.createContext<SidebarContextType | null>(null)

// Hook to access sidebar context
export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}
