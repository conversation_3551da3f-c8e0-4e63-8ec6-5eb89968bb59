
import { GlassCard } from "@/components/GlassCard";
import { ProgressBar } from "@/components/ProgressBar";
import { BottomNav } from "@/components/BottomNav";
import { Skeleton } from "@/components/ui/skeleton";
import { Tables } from "@/integrations/supabase/types";
import { useAuth } from "@/contexts/AuthContext";
import { UseMutationResult } from "@tanstack/react-query";
import { Member } from "@/hooks/useGuild";

interface GuildViewProps {
    guild: Tables<'guilds'>;
    members: Member[] | undefined;
    isLoadingMembers: boolean;
    leaveGuildMutation: UseMutationResult<void, Error, void, unknown>;
}

export function GuildView({ guild, members, isLoadingMembers, leaveGuildMutation }: GuildViewProps) {
    const { user } = useAuth();
    const guildMaxXp = 2800; // Placeholder for now

    return (
        <div className="pb-20 pt-6 min-h-screen bg-black/70">
            <div className="mx-4 mb-4">
                <GlassCard className="flex items-center py-4 px-6 justify-between bg-gradient-to-r from-electric to-purple">
                    <div>
                        <div className="font-bold text-xl uppercase tracking-wide gradient-title">{guild.name}</div>
                        <div className="text-xs text-white/80 mt-0.5">Members: {members?.length || 0}</div>
                    </div>
                    <button
                        onClick={() => leaveGuildMutation.mutate()}
                        disabled={leaveGuildMutation.isPending}
                        className={`py-1 px-4 rounded-xl font-semibold text-sm shadow-glow bg-white text-backdrop hover:bg-electric/90 disabled:opacity-50`}
                    >
                        {leaveGuildMutation.isPending ? 'Leaving...' : 'Leave'}
                    </button>
                </GlassCard>
            </div>

            <div className="mx-4 mb-6">
                <GlassCard className="flex flex-col py-4 px-5">
                    <div className="flex justify-between items-center mb-2">
                        <span className="font-semibold">Guild Level 4</span>
                        <span className="text-electric text-xs font-bold">{guild.xp} / {guildMaxXp} XP</span>
                    </div>
                    <ProgressBar percent={(guild.xp / guildMaxXp) * 100} />
                </GlassCard>
            </div>

            <div className="mx-4">
                <h2 className="gradient-title text-lg mb-2">Top Members</h2>
                <div className="flex flex-col gap-2">
                    {isLoadingMembers ? (
                        <div className="flex flex-col gap-2">
                            <Skeleton className="h-16 w-full" />
                            <Skeleton className="h-16 w-full" />
                            <Skeleton className="h-16 w-full" />
                        </div>
                    ) : (members as Member[])?.map((m, i) => (
                        <GlassCard key={m.user_id}
                            className={`flex items-center gap-3 py-2 px-3 ${m.user_id === user?.id ? "ring-2 ring-electric" : ""}`}
                            style={m.user_id === user?.id ? { background: "rgba(0,212,255,0.13)" } : {}}
                        >
                            <span className="font-bold text-xl w-6 text-center gradient-title">{i + 1}</span>
                            <img src={m.profiles?.avatar_url || 'https://avatar.vercel.sh/user'} className="w-8 h-8 rounded-full" />
                            <span className="font-semibold flex-1">{m.profiles?.username || "Anonymous"} {m.user_id === user?.id && <span className="text-xs text-electric">(You)</span>}</span>
                            <span className="text-electric font-bold">{Math.round(m.total_distance || 0)} XP</span>
                        </GlassCard>
                    ))}
                </div>
            </div>

            <BottomNav />
        </div>
    );
}
