
import { BottomNav } from "@/components/BottomNav";
import { GlassCard } from "@/components/GlassCard";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useMemo } from "react";
import { UserCashbackTable } from "@/components/admin/UserCashbackTable";
import { DistanceDistributionChart } from "@/components/admin/DistanceDistributionChart";

export default function Admin() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const { data: activities, isLoading: isActivitiesLoading, error: activitiesError } = useQuery({
    queryKey: ["user-activities-admin"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("user_activities")
        .select(`
          user_id,
          distance_km,
          activity_date,
          profiles (
            id,
            username
          )
        `)
        .gte('activity_date', thirtyDaysAgo.toISOString().split('T')[0]);

      if (error) {
        console.error("Error fetching user activities:", error);
        throw new Error(error.message);
      }
      
      return data.map(d => ({ ...d, profiles: Array.isArray(d.profiles) ? d.profiles[0] : d.profiles }));
    },
  });

  const { data: cashbackData, isLoading: isCashbackLoading, error: cashbackError } = useQuery({
    queryKey: ["cashback-payouts-admin"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('cashback_payouts')
        .select(`
          payout_amount,
          cashback_percentage,
          payout_year,
          payout_month,
          profiles!inner (
            username
          )
        `)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error("Error fetching cashback payouts:", error);
        throw new Error(error.message);
      }
      
      return data
        .map(p => ({
          username: p.profiles.username,
          payout_amount: p.payout_amount,
          cashback_percentage: p.cashback_percentage,
          payout_year: p.payout_year,
          payout_month: p.payout_month,
        }))
        .filter(p => p.username);
    },
  });

  const chartData = useMemo(() => {
    const distribution: { [key: string]: number } = {
      '0-1km': 0, '1-2km': 0, '2-3km': 0, '3-4km': 0, '4-5km': 0, '5km+': 0,
    };
    if (!activities) {
        return Object.entries(distribution).map(([name, count]) => ({ name, count }));
    };

    activities.forEach(act => {
      const d = Number(act.distance_km);
      if (d < 1) distribution['0-1km']++;
      else if (d < 2) distribution['1-2km']++;
      else if (d < 3) distribution['2-3km']++;
      else if (d < 4) distribution['3-4km']++;
      else if (d < 5) distribution['4-5km']++;
      else distribution['5km+']++;
    });

    return Object.entries(distribution).map(([name, count]) => ({ name, count }));
  }, [activities]);

  const isLoading = isActivitiesLoading || isCashbackLoading;
  const error = activitiesError || cashbackError;

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-right-bottom bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4">
          <h1 className="gradient-title text-2xl mb-4">Admin Dashboard</h1>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 text-electric animate-spin" />
            </div>
          ) : error ? (
            <GlassCard className="p-4 bg-red-500/20 border-red-500/50">
              <p className="text-white">Error loading data: {(error as Error).message}</p>
            </GlassCard>
          ) : (
            <div className="space-y-4">
              <GlassCard className="p-4">
                <p>Welcome, Admin! This is where you can manage the application.</p>
              </GlassCard>
              <UserCashbackTable data={cashbackData || []} />
              <DistanceDistributionChart data={chartData} />
            </div>
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
