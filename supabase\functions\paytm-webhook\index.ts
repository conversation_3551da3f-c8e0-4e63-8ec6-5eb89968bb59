// Paytm Webhook Handler Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { verifyChecksum, validatePaytmResponse, isSuccessfulTransaction } from '../_shared/paytm-checksum.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get Paytm configuration
    const PAYTM_KEY = Deno.env.get('PAYTM_MERCHANT_KEY')
    if (!PAYTM_KEY) {
      throw new Error('Paytm merchant key not configured')
    }

    // Parse webhook payload
    const webhookData = await req.json()
    const headers = Object.fromEntries(req.headers.entries())

    // Log webhook for debugging
    console.log('Received Paytm webhook:', webhookData)

    // Validate webhook payload
    if (!validatePaytmResponse(webhookData)) {
      throw new Error('Invalid webhook payload')
    }

    // Verify checksum
    if (webhookData.CHECKSUMHASH) {
      const isValidChecksum = await verifyChecksum(webhookData, PAYTM_KEY, webhookData.CHECKSUMHASH)
      if (!isValidChecksum) {
        throw new Error('Invalid checksum in webhook')
      }
    }

    // Find payment transaction
    const { data: paymentTransaction, error: transactionError } = await supabaseClient
      .from('payment_transactions')
      .select('*')
      .eq('order_id', webhookData.ORDERID)
      .single()

    if (transactionError || !paymentTransaction) {
      console.error('Payment transaction not found for order:', webhookData.ORDERID)
      // Still log the webhook even if transaction not found
    }

    // Log webhook event
    const { error: webhookLogError } = await supabaseClient
      .from('payment_webhooks')
      .insert({
        payment_transaction_id: paymentTransaction?.id || null,
        provider: 'paytm',
        webhook_type: `payment_${webhookData.STATUS.toLowerCase()}`,
        payload: webhookData,
        headers: headers,
        processed: false,
      })

    if (webhookLogError) {
      console.error('Error logging webhook:', webhookLogError)
    }

    // Process webhook if transaction found
    if (paymentTransaction) {
      // Determine final status
      let finalStatus = 'pending'
      if (webhookData.STATUS === 'TXN_SUCCESS') {
        finalStatus = 'success'
      } else if (webhookData.STATUS === 'TXN_FAILURE') {
        finalStatus = 'failed'
      }

      // Update payment transaction
      const { error: updateError } = await supabaseClient
        .from('payment_transactions')
        .update({
          status: finalStatus,
          transaction_id: webhookData.TXNID,
          gateway_response: webhookData,
          updated_at: new Date().toISOString(),
          ...(finalStatus === 'success' && { completed_at: new Date().toISOString() })
        })
        .eq('id', paymentTransaction.id)

      if (updateError) {
        console.error('Error updating payment transaction:', updateError)
        throw updateError
      }

      // Update Paytm transaction record
      const { error: paytmUpdateError } = await supabaseClient
        .from('paytm_transactions')
        .update({
          txn_id: webhookData.TXNID,
          bank_txn_id: webhookData.BANKTXNID,
          bank_name: webhookData.BANKNAME,
          gateway_name: webhookData.GATEWAYNAME,
          payment_mode: webhookData.PAYMENTMODE,
          resp_code: webhookData.RESPCODE,
          resp_msg: webhookData.RESPMSG,
          checksum_hash: webhookData.CHECKSUMHASH,
          txn_date: webhookData.TXNDATE ? new Date(webhookData.TXNDATE).toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('payment_transaction_id', paymentTransaction.id)

      if (paytmUpdateError) {
        console.error('Error updating Paytm transaction:', paytmUpdateError)
      }

      // Mark webhook as processed
      await supabaseClient
        .from('payment_webhooks')
        .update({
          processed: true,
          processed_at: new Date().toISOString()
        })
        .eq('payment_transaction_id', paymentTransaction.id)
        .eq('payload->ORDERID', webhookData.ORDERID)

      console.log(`Processed webhook for order ${webhookData.ORDERID} with status ${finalStatus}`)
    }

    // Return success response to Paytm
    return new Response(
      JSON.stringify({
        status: 'OK',
        message: 'Webhook processed successfully'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error processing Paytm webhook:', error)
    
    // Return error response
    return new Response(
      JSON.stringify({
        status: 'ERROR',
        message: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
