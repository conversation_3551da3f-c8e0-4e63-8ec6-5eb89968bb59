// Database testing hook
import { useState } from 'react';
import { TestResult } from '../types';
import { testConnection } from '../utils/connectionTest';
import { testProfiles } from '../utils/profilesTest';
import { testUserActivities } from '../utils/activitiesTest';
import { testOtherTables } from '../utils/tablesTest';

export function useDatabaseTests(userId?: string) {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addResult = (result: Omit<TestResult, 'timestamp'>) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date() }]);
  };

  const runAllTests = async () => {
    if (!userId) return;
    
    setIsRunningTests(true);
    setTestResults([]);
    
    addResult({
      table: 'system',
      operation: 'START',
      status: 'success',
      message: 'Starting database connectivity tests...',
    });

    // Test connection first
    const isConnected = await testConnection(addResult);
    
    if (isConnected) {
      await testProfiles(userId, addResult);
      await testUserActivities(userId, addResult);
      await testOtherTables(addResult);
    }
    
    addResult({
      table: 'system',
      operation: 'COMPLETE',
      status: 'success',
      message: 'Database tests completed',
    });
    
    setIsRunningTests(false);
  };

  return {
    testResults,
    isRunningTests,
    runAllTests,
  };
}
