
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface WithdrawalDrawerProps {
  balance: number;
  walletId: string;
}

export function WithdrawalDrawer({ balance, walletId }: WithdrawalDrawerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const withdrawalSchema = z.object({
    amount: z.coerce
      .number()
      .positive({ message: "Amount must be positive." })
      .max(balance, { message: "Withdrawal amount cannot exceed your balance." }),
    withdrawal_details: z.string().min(1, { message: "Withdrawal details are required." }),
  });

  const form = useForm<z.infer<typeof withdrawalSchema>>({
    resolver: zodResolver(withdrawalSchema),
    defaultValues: {
      amount: 0,
      withdrawal_details: "",
    },
  });

  const { mutate: submitWithdrawal, isPending } = useMutation({
    mutationFn: async (values: z.infer<typeof withdrawalSchema>) => {
      if (!user) throw new Error("User not authenticated.");

      const { error } = await supabase.from("withdrawal_requests").insert({
        user_id: user.id,
        wallet_id: walletId,
        amount: values.amount,
        withdrawal_details: { details: values.withdrawal_details },
        withdrawal_method: 'Manual Transfer', // Or derive from details
        status: 'pending',
      });

      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      toast.success("Withdrawal request submitted successfully!");
      queryClient.invalidateQueries({ queryKey: ["withdrawal_requests"] });
      setIsOpen(false);
      form.reset();
    },
    onError: (error) => {
      toast.error(`Failed to submit request: ${error.message}`);
    },
  });

  function onSubmit(values: z.infer<typeof withdrawalSchema>) {
    submitWithdrawal(values);
  }

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        <Button className="bg-electric hover:bg-electric/90 text-black">Withdraw Funds</Button>
      </DrawerTrigger>
      <DrawerContent className="bg-backdrop border-t-white/10 text-white">
        <div className="mx-auto w-full max-w-sm">
          <DrawerHeader>
            <DrawerTitle>Request Withdrawal</DrawerTitle>
            <DrawerDescription>
              Enter the amount and your withdrawal details (e.g., PayPal email).
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4 pb-0">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount ($)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" {...field} className="text-white bg-transparent" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="withdrawal_details"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Withdrawal Details</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., <EMAIL>" {...field} className="text-white bg-transparent" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full bg-electric hover:bg-electric/90 text-black" disabled={isPending}>
                  {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Submit Request
                </Button>
              </form>
            </Form>
          </div>
          <DrawerFooter>
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
