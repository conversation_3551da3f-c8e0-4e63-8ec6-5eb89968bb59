// Payment Test Suite Component (Development Only)
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  TestTube, 
  CheckCircle2, 
  XCircle, 
  Clock,
  Play,
  RotateCcw
} from 'lucide-react';
import { usePaytm } from '@/hooks/usePaytm';
import { validatePaymentRequest, formatAmountForPaytm } from '@/lib/payments';
import { generateOrderId } from '@/lib/paytm';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface TestCase {
  id: string;
  name: string;
  description: string;
  testFn: () => Promise<void>;
  status: 'pending' | 'running' | 'passed' | 'failed';
  error?: string;
}

export function PaymentTestSuite() {
  const { user } = useAuth();
  const [testAmount, setTestAmount] = useState('100');
  const [testResults, setTestResults] = useState<Record<string, TestCase>>({});
  const [isRunningAll, setIsRunningAll] = useState(false);

  const { startPayment, verifyPayment, resetCheckout } = usePaytm({
    onSuccess: (response) => {
      console.log('Test payment success:', response);
    },
    onFailure: (error) => {
      console.log('Test payment failure:', error);
    }
  });

  const updateTestStatus = (testId: string, status: TestCase['status'], error?: string) => {
    setTestResults(prev => ({
      ...prev,
      [testId]: {
        ...prev[testId],
        status,
        error
      }
    }));
  };

  const testCases: TestCase[] = [
    {
      id: 'validation-valid-amount',
      name: 'Valid Amount Validation',
      description: 'Test validation with valid payment amount',
      status: 'pending',
      testFn: async () => {
        const result = validatePaymentRequest({
          amount: 100,
          orderId: 'TEST_001',
          customerId: user?.id || 'test_user',
          customerEmail: '<EMAIL>'
        });
        
        if (!result.isValid) {
          throw new Error(`Validation failed: ${result.errors.join(', ')}`);
        }
      }
    },
    {
      id: 'validation-invalid-amount',
      name: 'Invalid Amount Validation',
      description: 'Test validation with invalid payment amount',
      status: 'pending',
      testFn: async () => {
        const result = validatePaymentRequest({
          amount: -10,
          orderId: 'TEST_002',
          customerId: user?.id || 'test_user'
        });
        
        if (result.isValid) {
          throw new Error('Validation should have failed for negative amount');
        }
      }
    },
    {
      id: 'order-id-generation',
      name: 'Order ID Generation',
      description: 'Test unique order ID generation',
      status: 'pending',
      testFn: async () => {
        const orderId1 = generateOrderId('TEST');
        const orderId2 = generateOrderId('TEST');
        
        if (orderId1 === orderId2) {
          throw new Error('Order IDs should be unique');
        }
        
        if (!orderId1.startsWith('TEST_')) {
          throw new Error('Order ID should start with prefix');
        }
      }
    },
    {
      id: 'amount-formatting',
      name: 'Amount Formatting',
      description: 'Test amount formatting for Paytm',
      status: 'pending',
      testFn: async () => {
        const formatted = formatAmountForPaytm(100.5);
        if (formatted !== '100.50') {
          throw new Error(`Expected '100.50', got '${formatted}'`);
        }
        
        const formatted2 = formatAmountForPaytm(100);
        if (formatted2 !== '100.00') {
          throw new Error(`Expected '100.00', got '${formatted2}'`);
        }
      }
    },
    {
      id: 'payment-initiation',
      name: 'Payment Initiation',
      description: 'Test payment initiation with valid data',
      status: 'pending',
      testFn: async () => {
        if (!user) {
          throw new Error('User not authenticated for test');
        }
        
        const orderId = generateOrderId('TEST');
        await startPayment({
          orderId,
          amount: testAmount,
          customerId: user.id,
          customerEmail: user.email,
          paymentModes: ['UPI']
        });
      }
    }
  ];

  // Initialize test results
  if (Object.keys(testResults).length === 0) {
    const initialResults: Record<string, TestCase> = {};
    testCases.forEach(test => {
      initialResults[test.id] = { ...test };
    });
    setTestResults(initialResults);
  }

  const runTest = async (testId: string) => {
    const test = testResults[testId];
    if (!test) return;

    updateTestStatus(testId, 'running');
    
    try {
      await test.testFn();
      updateTestStatus(testId, 'passed');
      toast.success(`Test passed: ${test.name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateTestStatus(testId, 'failed', errorMessage);
      toast.error(`Test failed: ${test.name}`);
    }
  };

  const runAllTests = async () => {
    setIsRunningAll(true);
    
    for (const test of testCases) {
      await runTest(test.id);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunningAll(false);
    
    const results = Object.values(testResults);
    const passed = results.filter(t => t.status === 'passed').length;
    const failed = results.filter(t => t.status === 'failed').length;
    
    toast.info(`Tests completed: ${passed} passed, ${failed} failed`);
  };

  const resetTests = () => {
    const resetResults: Record<string, TestCase> = {};
    testCases.forEach(test => {
      resetResults[test.id] = { ...test, status: 'pending', error: undefined };
    });
    setTestResults(resetResults);
    resetCheckout();
  };

  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle2 className="h-4 w-4 text-green-400" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-400" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-400 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestCase['status']) => {
    switch (status) {
      case 'passed':
        return 'bg-green-500/20 text-green-400 border-green-500/50';
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/50';
      case 'running':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/50';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/50';
    }
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="bg-black/40 border-white/10">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <TestTube className="h-5 w-5 text-electric" />
          Payment Test Suite
        </CardTitle>
        <CardDescription className="text-white/60">
          Development testing tools for payment functionality
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Test Configuration */}
        <div className="space-y-2">
          <Label htmlFor="test-amount" className="text-white">Test Amount (₹)</Label>
          <Input
            id="test-amount"
            type="number"
            value={testAmount}
            onChange={(e) => setTestAmount(e.target.value)}
            className="bg-white/10 border-white/20 text-white"
            placeholder="Enter test amount"
          />
        </div>

        <Separator className="bg-white/10" />

        {/* Test Controls */}
        <div className="flex gap-2">
          <Button
            onClick={runAllTests}
            disabled={isRunningAll}
            className="bg-electric hover:bg-purple text-white"
          >
            <Play className="h-4 w-4 mr-2" />
            Run All Tests
          </Button>
          <Button
            onClick={resetTests}
            variant="outline"
            className="border-white/20 text-white/80 hover:bg-white/10"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>

        {/* Test Results */}
        <div className="space-y-2">
          {Object.values(testResults).map((test) => (
            <Card key={test.id} className="bg-white/5 border-white/10">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <h4 className="font-medium text-white text-sm">{test.name}</h4>
                      <p className="text-xs text-white/60">{test.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(test.status)}>
                      {test.status}
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => runTest(test.id)}
                      disabled={test.status === 'running' || isRunningAll}
                      className="border-white/20 text-white/80 hover:bg-white/10"
                    >
                      Run
                    </Button>
                  </div>
                </div>
                {test.error && (
                  <div className="mt-2 p-2 bg-red-500/10 border border-red-500/30 rounded text-xs text-red-300">
                    {test.error}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
