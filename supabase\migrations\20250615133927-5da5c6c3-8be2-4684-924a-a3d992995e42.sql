
-- Add activity type to user_activities table
ALTER TABLE public.user_activities
ADD COLUMN activity_type TEXT NOT NULL DEFAULT 'Run';

-- Insert sample activities for the logged-in user to populate the recent activity list.
-- This uses a static user ID for demonstration purposes.
INSERT INTO public.user_activities (user_id, distance_km, activity_type, activity_date)
SELECT '35d745e0-dae5-4372-a154-fe3220191cf9', 5, 'Run', '2025-06-08'
WHERE NOT EXISTS (SELECT 1 FROM public.user_activities WHERE user_id = '35d745e0-dae5-4372-a154-fe3220191cf9' AND activity_date = '2025-06-08');

INSERT INTO public.user_activities (user_id, distance_km, activity_type, activity_date)
SELECT '35d745e0-dae5-4372-a154-fe3220191cf9', 2.5, 'Cycling', '2025-06-05'
WHERE NOT EXISTS (SELECT 1 FROM public.user_activities WHERE user_id = '35d745e0-dae5-4372-a154-fe3220191cf9' AND activity_date = '2025-06-05');

INSERT INTO public.user_activities (user_id, distance_km, activity_type, activity_date)
SELECT '35d745e0-dae5-4372-a154-fe3220191cf9', 3, 'Run', '2025-06-03'
WHERE NOT EXISTS (SELECT 1 FROM public.user_activities WHERE user_id = '35d745e0-dae5-4372-a154-fe3220191cf9' AND activity_date = '2025-06-03');
