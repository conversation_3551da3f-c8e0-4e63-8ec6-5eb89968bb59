/**
 * Comprehensive Chat Functionality Tests
 * Tests both desktop and mobile interfaces for the Solo Grind chat system
 * Covers global chat, guild chat, and positioning fixes
 */

const { test, expect } = require('@playwright/test');

// Test configuration
const BASE_URL = 'http://localhost:8080';
const MOBILE_VIEWPORT = { width: 375, height: 667 }; // iPhone SE
const DESKTOP_VIEWPORT = { width: 1280, height: 720 };

test.describe('Chat Functionality Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Navigate to the chat page
    await page.goto(`${BASE_URL}/chat`);
    await page.waitForLoadState('networkidle');
  });

  test.describe('Desktop Interface', () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize(DESKTOP_VIEWPORT);
    });

    test('should display chat interface with proper layout', async ({ page }) => {
      // Verify chat tabs are visible
      await expect(page.getByRole('tab', { name: 'Global Chat' })).toBeVisible();
      await expect(page.getByRole('tab', { name: /Guild Chat/ })).toBeVisible();
      
      // Verify global chat is selected by default
      await expect(page.getByRole('tab', { name: 'Global Chat' })).toHaveAttribute('aria-selected', 'true');
      
      // Verify chat input is positioned correctly at bottom
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await expect(chatInput).toBeVisible();
      
      // Verify bottom navigation is present
      const bottomNav = page.locator('nav').last();
      await expect(bottomNav).toBeVisible();
    });

    test('should send global chat messages successfully', async ({ page }) => {
      const testMessage = `Test message ${Date.now()}`;
      
      // Type and send message
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await chatInput.fill(testMessage);
      await chatInput.press('Enter');
      
      // Verify message appears in chat
      await expect(page.getByText(testMessage)).toBeVisible();
      
      // Verify input field is cleared
      await expect(chatInput).toHaveValue('');
    });

    test('should switch between global and guild chat tabs', async ({ page }) => {
      // Click on Guild Chat tab
      await page.getByRole('tab', { name: /Guild Chat/ }).click();
      
      // Verify guild chat tab is selected
      await expect(page.getByRole('tab', { name: /Guild Chat/ })).toHaveAttribute('aria-selected', 'true');
      
      // Check if user is in a guild
      const guildChatContent = page.getByRole('tabpanel', { name: /Guild Chat/ });
      await expect(guildChatContent).toBeVisible();
      
      // Switch back to global chat
      await page.getByRole('tab', { name: 'Global Chat' }).click();
      await expect(page.getByRole('tab', { name: 'Global Chat' })).toHaveAttribute('aria-selected', 'true');
    });

    test('should display guild membership status correctly', async ({ page }) => {
      // Click on Guild Chat tab
      await page.getByRole('tab', { name: /Guild Chat/ }).click();
      
      const guildChatPanel = page.getByRole('tabpanel', { name: /Guild Chat/ });
      
      // Check for either guild membership or no-guild message
      const hasGuildMembership = await page.getByText('Type a message to guild chat').isVisible();
      const noGuildMessage = await page.getByText("You're not in a guild yet").isVisible();
      
      expect(hasGuildMembership || noGuildMessage).toBeTruthy();
      
      if (hasGuildMembership) {
        // User is in a guild - verify guild name appears in tab
        const guildTab = page.getByRole('tab', { name: /Guild Chat/ });
        const tabText = await guildTab.textContent();
        expect(tabText).toContain('Guild Chat');
      }
    });
  });

  test.describe('Mobile Interface', () => {
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize(MOBILE_VIEWPORT);
    });

    test('should display mobile-optimized chat interface', async ({ page }) => {
      // Verify chat interface adapts to mobile viewport
      await expect(page.getByRole('tab', { name: 'Global Chat' })).toBeVisible();
      await expect(page.getByRole('tab', { name: /Guild Chat/ })).toBeVisible();
      
      // Verify chat input is accessible on mobile
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await expect(chatInput).toBeVisible();
      
      // Verify bottom navigation is properly positioned
      const bottomNav = page.locator('nav').last();
      await expect(bottomNav).toBeVisible();
    });

    test('should handle mobile chat input correctly', async ({ page }) => {
      const testMessage = `Mobile test ${Date.now()}`;
      
      // Test mobile input interaction
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await chatInput.tap();
      await chatInput.fill(testMessage);
      
      // Send message using mobile interaction
      await chatInput.press('Enter');
      
      // Verify message appears
      await expect(page.getByText(testMessage)).toBeVisible();
    });

    test('should maintain proper spacing with mobile keyboard', async ({ page }) => {
      // Focus on input to potentially trigger mobile keyboard
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await chatInput.focus();
      
      // Verify input remains visible and accessible
      await expect(chatInput).toBeVisible();
      
      // Verify bottom navigation doesn't overlap with input
      const bottomNav = page.locator('nav').last();
      const inputBox = await chatInput.boundingBox();
      const navBox = await bottomNav.boundingBox();
      
      if (inputBox && navBox) {
        expect(inputBox.y).toBeLessThan(navBox.y);
      }
    });
  });

  test.describe('Chat Input Positioning', () => {
    test('should position chat input at bottom of container', async ({ page }) => {
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      const bottomNav = page.locator('nav').last();
      
      // Get bounding boxes
      const inputBox = await chatInput.boundingBox();
      const navBox = await bottomNav.boundingBox();
      
      // Verify input is above bottom navigation
      expect(inputBox.y).toBeLessThan(navBox.y);
      
      // Verify input is near the bottom of the chat area
      const viewport = page.viewportSize();
      const distanceFromBottom = viewport.height - inputBox.y - inputBox.height;
      expect(distanceFromBottom).toBeLessThan(100); // Should be close to bottom
    });

    test('should maintain input position when switching tabs', async ({ page }) => {
      // Get initial position
      const globalInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      const initialBox = await globalInput.boundingBox();
      
      // Switch to guild chat
      await page.getByRole('tab', { name: /Guild Chat/ }).click();
      
      // Check if guild input exists (user might not be in a guild)
      const guildInput = page.getByRole('textbox', { name: 'Type a message to guild chat' });
      const guildInputExists = await guildInput.isVisible();
      
      if (guildInputExists) {
        const guildBox = await guildInput.boundingBox();
        
        // Verify position consistency
        expect(Math.abs(guildBox.y - initialBox.y)).toBeLessThan(10);
      }
      
      // Switch back to global
      await page.getByRole('tab', { name: 'Global Chat' }).click();
      const finalBox = await globalInput.boundingBox();
      
      // Verify position is maintained
      expect(Math.abs(finalBox.y - initialBox.y)).toBeLessThan(5);
    });
  });

  test.describe('Real-time Features', () => {
    test('should display optimistic updates', async ({ page }) => {
      const testMessage = `Optimistic test ${Date.now()}`;
      
      // Send message
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await chatInput.fill(testMessage);
      await chatInput.press('Enter');
      
      // Look for optimistic update indicators
      const sendingIndicator = page.getByText('Sending...');
      
      // Message should appear immediately (optimistic update)
      await expect(page.getByText(testMessage)).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle empty messages gracefully', async ({ page }) => {
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      
      // Try to send empty message
      await chatInput.press('Enter');
      
      // Send button should remain disabled for empty input
      const sendButton = page.getByRole('button').filter({ hasText: /send/i });
      if (await sendButton.isVisible()) {
        await expect(sendButton).toBeDisabled();
      }
    });

    test('should handle very long messages', async ({ page }) => {
      const longMessage = 'A'.repeat(600); // Longer than 500 char limit
      
      const chatInput = page.getByRole('textbox', { name: 'Type a message to global chat' });
      await chatInput.fill(longMessage);
      
      // Should show character count warning
      const charWarning = page.getByText(/characters remaining/);
      if (await charWarning.isVisible()) {
        expect(await charWarning.textContent()).toContain('remaining');
      }
    });
  });
});
