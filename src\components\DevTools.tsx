// Development tools component - only visible in development mode
import { useState } from 'react';
import { Button } from './ui/button';
import { ChevronDown, ChevronUp, Code } from 'lucide-react';
import { isDevelopment } from '@/lib/config';
import {
  EnvironmentPanel,
  PerformancePanel,
  LogsPanel,
  QuickActions,
  useDevTools
} from './dev-tools';

export const DevTools = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isDevelopment()) {
    return null;
  }

  const { envInfo, performanceSummary, recentLogs, exportData } = useDevTools();

  if (!isExpanded) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <Button
          onClick={() => setIsExpanded(true)}
          size="sm"
          className="bg-purple/80 hover:bg-purple text-white shadow-lg"
        >
          <Code className="h-4 w-4 mr-2" />
          Dev <PERSON>ls
          <ChevronUp className="h-4 w-4 ml-2" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 z-50 w-80 max-h-96 overflow-y-auto">
      <div className="bg-black/90 border border-purple/50 rounded-lg shadow-xl p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2 text-white text-sm font-medium">
            <Code className="h-4 w-4" />
            Development Tools
          </div>
          <Button
            onClick={() => setIsExpanded(false)}
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0 text-white/60 hover:text-white"
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-3">
          <EnvironmentPanel envInfo={envInfo} />
          <PerformancePanel performanceSummary={performanceSummary} />
          <LogsPanel recentLogs={recentLogs} />
          <QuickActions onExportData={exportData} />
        </div>
      </div>
    </div>
  );
};
