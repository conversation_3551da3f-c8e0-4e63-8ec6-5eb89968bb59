
import React from 'react';
import * as LucideIcons from 'lucide-react';

type IconName = keyof typeof LucideIcons;

type DynamicIconProps = {
  name: IconName | string;
} & LucideIcons.LucideProps;

const iconMap = LucideIcons;

export const DynamicIcon = ({ name, ...props }: DynamicIconProps) => {
  const IconComponent = (iconMap as any)[name];

  if (!IconComponent) {
    // Return a default icon or null if the icon name is not found
    return <LucideIcons.HelpCircle {...props} />;
  }

  return <IconComponent {...props} />;
};
