
import { AvatarGlow } from "@/components/AvatarGlow";
import { TierBadge } from "@/components/TierBadge";
import { GlassCard } from "@/components/GlassCard";
import { BottomNav } from "@/components/BottomNav";
import { Link } from "react-router-dom";
import { Award, ChevronRight, Zap, Wallet } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { Tables } from "@/integrations/supabase/types";

const DEFAULT_AVATAR = "https://randomuser.me/api/portraits/men/1.jpg";

export default function Profile() {
  const { isSubscribed, profile, user } = useAuth();

  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['user-leaderboard-stats', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('user_leaderboard_stats')
        .select('total_distance')
        .eq('user_id', user.id)
        .single();
      if (error) {
        // It's okay if no record is found, maybe they have no activities yet.
        if (error.code !== 'PGRST116') {
            console.error('Error fetching user stats:', error);
        }
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  const { data: recentActivities, isLoading: isLoadingActivities } = useQuery({
    queryKey: ['recent-activities', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const { data, error } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', user.id)
        .order('activity_date', { ascending: false })
        .limit(3);
      if (error) {
        console.error('Error fetching recent activities:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  return (
    <div className="pb-20 pt-6 min-h-screen bg-backdrop">
      {/* Avatar with Glow */}
      <div className="flex flex-col items-center gap-3">
        <AvatarGlow src={profile?.avatar_url || DEFAULT_AVATAR} size={90} className="animate-glow" />
        <div className="flex items-center gap-2">
          <span className="font-bold text-2xl tracking-wide">{profile?.username || 'SkyRunner'}</span>
          <TierBadge tier="B" />
        </div>
        <span className="text-electric text-xs font-semibold uppercase">Level 17</span>
      </div>

      {/* Stats Grid */}
      <div className="mt-6 grid grid-cols-3 gap-4 px-5">
        <GlassCard className="flex flex-col items-center py-3">
          <div className="stat-label">Distance</div>
          <div className="stat-value">{isLoadingStats ? '...' : `${stats?.total_distance || 0}km`}</div>
        </GlassCard>
        <GlassCard className="flex flex-col items-center py-3">
          <div className="stat-label">Trophies</div>
          <div className="stat-value">15</div>
        </GlassCard>
        <GlassCard className="flex flex-col items-center py-3">
          <div className="stat-label">Guild XP</div>
          <div className="stat-value">777</div>
        </GlassCard>
      </div>

      {/* Achievements Link */}
      <div className="mt-7 px-5">
        <Link to="/achievements">
          <GlassCard className="flex justify-between items-center px-4 py-3 hover:bg-white/5 transition-colors shadow-glow">
            <div className="flex items-center gap-3">
              <Award className="text-electric" size={24} />
              <span className="font-bold text-white">My Achievements</span>
            </div>
            <ChevronRight className="text-white/50" size={20} />
          </GlassCard>
        </Link>
      </div>

      {/* Wallet Link */}
      <div className="mt-4 px-5">
        <Link to="/wallet">
          <GlassCard className="flex justify-between items-center px-4 py-3 hover:bg-white/5 transition-colors shadow-glow">
            <div className="flex items-center gap-3">
              <Wallet className="text-electric" size={24} />
              <span className="font-bold text-white">My Wallet</span>
            </div>
            <ChevronRight className="text-white/50" size={20} />
          </GlassCard>
        </Link>
      </div>

      {/* Subscription Link */}
      <div className="mt-4 px-5">
        <Link to="/subscription">
          <GlassCard className="flex justify-between items-center px-4 py-3 hover:bg-white/5 transition-colors shadow-glow">
            <div className="flex items-center gap-3">
              <Zap className="text-electric" size={24} />
              <div>
                <span className="font-bold text-white">
                  {isSubscribed ? "Manage Plan" : "Upgrade to Pro"}
                </span>
                {!isSubscribed && (
                  <p className="text-xs text-white/50">
                    Earn daily cashback on your activities!
                  </p>
                )}
              </div>
            </div>
            <ChevronRight className="text-white/50" size={20} />
          </GlassCard>
        </Link>
      </div>

      {/* Recent Activity */}
      <div className="mt-7 px-5">
        <h2 className="gradient-title text-lg mb-2">Recent Activity</h2>
        <div className="flex flex-col gap-3">
          {isLoadingActivities ? (
            <GlassCard className="flex justify-center items-center p-4">
              <span className="text-white/50">Loading activities...</span>
            </GlassCard>
          ) : recentActivities && recentActivities.length > 0 ? (
            recentActivities.map((entry: Tables<'user_activities'>) => (
              <GlassCard
                key={entry.id}
                className="flex justify-between items-center px-4 py-2"
              >
                <span className="font-medium text-white/80">{entry.activity_type}</span>
                <span className="text-electric font-semibold">{entry.distance_km} km</span>
                <span className="text-xs text-white/50">{format(new Date(entry.activity_date), 'yyyy-MM-dd')}</span>
              </GlassCard>
            ))
          ) : (
            <GlassCard className="flex justify-center items-center p-4">
              <span className="text-white/50">No recent activities.</span>
            </GlassCard>
          )}
        </div>
      </div>

      <BottomNav />
    </div>
  );
}
