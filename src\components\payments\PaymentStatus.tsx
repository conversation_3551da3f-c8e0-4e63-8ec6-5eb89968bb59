import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  RefreshCw, 
  ArrowLeft,
  CreditCard,
  Calendar,
  Hash,
  IndianRupee
} from 'lucide-react';
import { usePaytm } from '@/hooks/usePaytm';
import { PaytmTransactionResponse } from '@/lib/paytm';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface PaymentStatusProps {
  orderId: string;
  onBack?: () => void;
  onSuccess?: (response: PaytmTransactionResponse) => void;
  onFailure?: (error: string) => void;
  autoVerify?: boolean;
  className?: string;
}

type PaymentStatus = 'pending' | 'success' | 'failed' | 'verifying';

const statusConfig = {
  pending: {
    icon: Clock,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20 border-yellow-500/50',
    title: 'Payment Pending',
    description: 'Your payment is being processed',
  },
  success: {
    icon: CheckCircle2,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20 border-green-500/50',
    title: 'Payment Successful',
    description: 'Your payment has been completed successfully',
  },
  failed: {
    icon: XCircle,
    color: 'text-red-400',
    bgColor: 'bg-red-500/20 border-red-500/50',
    title: 'Payment Failed',
    description: 'Your payment could not be processed',
  },
  verifying: {
    icon: RefreshCw,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20 border-blue-500/50',
    title: 'Verifying Payment',
    description: 'Please wait while we verify your payment',
  },
};

export function PaymentStatus({ 
  orderId, 
  onBack, 
  onSuccess, 
  onFailure,
  autoVerify = true,
  className 
}: PaymentStatusProps) {
  const [status, setStatus] = useState<PaymentStatus>('pending');
  const [transactionData, setTransactionData] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);

  const { verifyPayment, isVerifying } = usePaytm({
    onSuccess: (response) => {
      setStatus('success');
      setTransactionData(response);
      onSuccess?.(response);
    },
    onFailure: (error) => {
      setStatus('failed');
      setTransactionData({ error });
      onFailure?.(error);
    },
  });

  // Auto-verify payment on mount
  useEffect(() => {
    if (autoVerify && orderId) {
      handleVerifyPayment();
    }
  }, [orderId, autoVerify]);

  const handleVerifyPayment = async () => {
    setStatus('verifying');
    try {
      await verifyPayment(orderId);
    } catch (error) {
      console.error('Verification error:', error);
      setStatus('failed');
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    handleVerifyPayment();
  };

  const currentConfig = statusConfig[isVerifying ? 'verifying' : status];
  const StatusIcon = currentConfig.icon;

  return (
    <Card className={cn("bg-black/40 border-white/10", className)}>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className={cn(
            "p-4 rounded-full border-2",
            currentConfig.bgColor
          )}>
            <StatusIcon className={cn(
              "h-8 w-8",
              currentConfig.color,
              isVerifying && "animate-spin"
            )} />
          </div>
        </div>
        
        <CardTitle className="text-white">
          {currentConfig.title}
        </CardTitle>
        <CardDescription className="text-white/60">
          {currentConfig.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Order Details */}
        <div className="space-y-3">
          <h3 className="font-semibold text-white flex items-center gap-2">
            <Hash className="h-4 w-4 text-electric" />
            Transaction Details
          </h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-white/60">Order ID:</span>
              <span className="text-white font-mono">{orderId}</span>
            </div>
            
            {transactionData?.txnId && (
              <div className="flex justify-between">
                <span className="text-white/60">Transaction ID:</span>
                <span className="text-white font-mono">{transactionData.txnId}</span>
              </div>
            )}
            
            {transactionData?.amount && (
              <div className="flex justify-between">
                <span className="text-white/60">Amount:</span>
                <span className="text-white flex items-center gap-1">
                  <IndianRupee className="h-3 w-3" />
                  {transactionData.amount}
                </span>
              </div>
            )}
            
            {transactionData?.paymentMode && (
              <div className="flex justify-between">
                <span className="text-white/60">Payment Method:</span>
                <Badge variant="secondary" className="bg-electric/20 text-electric">
                  {transactionData.paymentMode}
                </Badge>
              </div>
            )}
            
            {transactionData?.txnDate && (
              <div className="flex justify-between">
                <span className="text-white/60">Date & Time:</span>
                <span className="text-white flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(new Date(transactionData.txnDate), 'dd MMM yyyy, hh:mm a')}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Status-specific content */}
        {status === 'success' && transactionData && (
          <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle2 className="h-4 w-4 text-green-400" />
              <span className="font-medium text-green-300">Payment Confirmed</span>
            </div>
            <p className="text-sm text-green-200/80">
              Your wallet has been credited with ₹{transactionData.amount}
            </p>
          </div>
        )}

        {status === 'failed' && (
          <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <XCircle className="h-4 w-4 text-red-400" />
              <span className="font-medium text-red-300">Payment Failed</span>
            </div>
            <p className="text-sm text-red-200/80">
              {transactionData?.error || transactionData?.respMsg || 'Payment could not be processed. Please try again.'}
            </p>
          </div>
        )}

        {status === 'pending' && (
          <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-yellow-400" />
              <span className="font-medium text-yellow-300">Processing Payment</span>
            </div>
            <p className="text-sm text-yellow-200/80">
              Your payment is being processed. This may take a few moments.
            </p>
          </div>
        )}

        <Separator className="bg-white/10" />

        {/* Action Buttons */}
        <div className="flex gap-3">
          {onBack && (
            <Button
              variant="outline"
              onClick={onBack}
              className="flex-1 border-white/20 text-white/80 hover:bg-white/10"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          
          {(status === 'failed' || status === 'pending') && (
            <Button
              onClick={handleRetry}
              disabled={isVerifying}
              className="flex-1 bg-electric hover:bg-purple text-white"
            >
              {isVerifying ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {status === 'failed' ? 'Retry Payment' : 'Check Status'}
                </>
              )}
            </Button>
          )}
        </div>

        {/* Retry Information */}
        {retryCount > 0 && (
          <div className="text-xs text-white/50 text-center">
            Verification attempts: {retryCount}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
