import { Target } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { calculateProgress, calculateTodayDistance } from './utils/formatters';

interface TodayProgressCardProps {
  todayActivities: Array<{ distance_km: number }> | undefined;
  dailyGoal: number;
  isLoading: boolean;
}

export function TodayProgressCard({ todayActivities, dailyGoal, isLoading }: TodayProgressCardProps) {
  const todayDistance = calculateTodayDistance(todayActivities);
  const dailyProgress = calculateProgress(todayDistance, dailyGoal);

  return (
    <GlassCard className="mb-6 p-4 max-w-md mx-auto">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Target className="text-electric" size={20} />
          <span className="text-white/80 font-semibold">Today's Progress</span>
        </div>
        <span className="text-xs text-electric font-semibold">
          {isLoading ? '...' : `${todayDistance.toFixed(1)} / ${dailyGoal} km`}
        </span>
      </div>
      <div className="w-full bg-white/10 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-electric to-purple h-2 rounded-full transition-all duration-300"
          style={{ width: `${isLoading ? 0 : dailyProgress}%` }}
        />
      </div>
      <div className="flex justify-between text-xs text-white/60 mt-1">
        <span>{isLoading ? '...' : `${dailyProgress.toFixed(0)}% complete`}</span>
        <span>{isLoading ? '...' : `${(dailyGoal - todayDistance).toFixed(1)} km to go`}</span>
      </div>
    </GlassCard>
  );
}
