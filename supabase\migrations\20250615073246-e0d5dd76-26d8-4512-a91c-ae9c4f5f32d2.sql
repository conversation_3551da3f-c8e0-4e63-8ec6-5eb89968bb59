
-- Drop the old real-time cashback system
DROP TRIGGER IF EXISTS on_user_activity_change ON public.user_activities;
DROP FUNCTION IF EXISTS public.update_user_cashback();
DROP TABLE IF EXISTS public.user_cashback;

-- Create enum types for new wallet functionality
CREATE TYPE public.wallet_transaction_type AS ENUM ('cashback_credit', 'withdrawal_debit', 'withdrawal_fee', 'adjustment_credit', 'adjustment_debit', 'signup_bonus');
CREATE TYPE public.withdrawal_status AS ENUM ('pending', 'approved', 'rejected', 'processing', 'completed', 'failed');

-- Create wallets table
CREATE TABLE public.wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    balance NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE public.wallets IS 'Stores user wallet balances.';
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own wallet" ON public.wallets FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all wallets" ON public.wallets FOR ALL USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- Create wallet transactions table
CREATE TABLE public.wallet_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE CASCADE,
    amount NUMERIC(10, 2) NOT NULL,
    type public.wallet_transaction_type NOT NULL,
    description TEXT,
    related_activity_id UUID REFERENCES public.user_activities(id) ON DELETE SET NULL,
    related_payout_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE public.wallet_transactions IS 'Logs all transactions for user wallets.';
ALTER TABLE public.wallet_transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own transactions" ON public.wallet_transactions FOR SELECT USING (EXISTS (SELECT 1 FROM public.wallets WHERE id = wallet_id AND user_id = auth.uid()));
CREATE POLICY "Admins can manage all transactions" ON public.wallet_transactions FOR ALL USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- Create withdrawal requests table
CREATE TABLE public.withdrawal_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE CASCADE,
    amount NUMERIC(10, 2) NOT NULL,
    status public.withdrawal_status NOT NULL DEFAULT 'pending',
    withdrawal_method TEXT,
    withdrawal_details JSONB,
    requested_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    processed_at TIMESTAMPTZ,
    admin_notes TEXT
);
COMMENT ON TABLE public.withdrawal_requests IS 'Manages user requests to withdraw funds from their wallet.';
ALTER TABLE public.withdrawal_requests ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage their own withdrawal requests" ON public.withdrawal_requests FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Admins can manage all withdrawal requests" ON public.withdrawal_requests FOR ALL USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- Create cashback payouts table to log monthly payouts
CREATE TABLE public.cashback_payouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    payout_year INT NOT NULL,
    payout_month INT NOT NULL,
    total_distance_km NUMERIC(10, 2) NOT NULL,
    cashback_percentage NUMERIC(5, 2) NOT NULL,
    payout_amount NUMERIC(10, 2) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(user_id, payout_year, payout_month)
);
COMMENT ON TABLE public.cashback_payouts IS 'Logs successful monthly cashback payouts to prevent duplicates.';
ALTER TABLE public.cashback_payouts ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can see their own payouts" ON public.cashback_payouts FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Admins can see all payouts" ON public.cashback_payouts FOR SELECT USING (EXISTS (SELECT 1 FROM public.user_roles WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'));

-- Add foreign key from wallet_transactions to cashback_payouts
ALTER TABLE public.wallet_transactions
ADD CONSTRAINT fk_payout
FOREIGN KEY (related_payout_id)
REFERENCES public.cashback_payouts(id)
ON DELETE SET NULL;

-- Function to create a wallet, now separate from handle_new_user for clarity
CREATE OR REPLACE FUNCTION public.create_user_wallet(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.wallets (user_id)
    VALUES (p_user_id)
    ON CONFLICT (user_id) DO NOTHING;
END;
$$;

-- Modify handle_new_user to call the wallet creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, username, avatar_url)
  VALUES (new.id, new.raw_user_meta_data->>'username', new.raw_user_meta_data->>'avatar_url');
  
  INSERT INTO public.user_roles (user_id, role)
  VALUES (new.id, 'user');
  
  -- Create a wallet for the new user
  PERFORM public.create_user_wallet(new.id);
  
  RETURN new;
END;
$$;

-- Retroactively create wallets for all existing users that might not have one
SELECT public.create_user_wallet(id) FROM public.profiles;

-- The main function to process monthly cashback
CREATE OR REPLACE FUNCTION public.process_monthly_cashback_payouts(p_year INTEGER, p_month INTEGER)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    usr RECORD;
    v_wallet_id UUID;
    v_total_distance NUMERIC;
    v_cashback_base_sum NUMERIC;
    v_cashback_percentage NUMERIC;
    v_payout_amount NUMERIC;
    v_payout_id UUID;
    v_days_in_month INT;
BEGIN
    v_days_in_month := EXTRACT(DAY FROM (make_date(p_year, p_month, 1) + INTERVAL '1 month - 1 day'));

    FOR usr IN
        SELECT p.id as user_id
        FROM public.profiles p
        JOIN public.user_activities ua ON p.id = ua.user_id
        WHERE EXTRACT(YEAR FROM ua.activity_date) = p_year
          AND EXTRACT(MONTH FROM ua.activity_date) = p_month
        GROUP BY p.id
    LOOP
        -- Check if payout already exists
        IF NOT EXISTS (
            SELECT 1 FROM public.cashback_payouts
            WHERE user_id = usr.user_id AND payout_year = p_year AND payout_month = p_month
        ) THEN
            -- Calculate metrics for the month
            SELECT
                SUM(ua.distance_km),
                SUM(public.get_cashback_base(ua.distance_km))
            INTO
                v_total_distance,
                v_cashback_base_sum
            FROM public.user_activities ua
            WHERE ua.user_id = usr.user_id
              AND EXTRACT(YEAR FROM ua.activity_date) = p_year
              AND EXTRACT(MONTH FROM ua.activity_date) = p_month;

            IF v_total_distance > 0 THEN
                v_cashback_percentage := (COALESCE(v_cashback_base_sum, 0) / v_days_in_month) * 100.0;
                -- Payout is based on distance, but let's assume a simple $0.1 per km for now.
                -- This can be made more complex later.
                v_payout_amount := v_total_distance * 0.10; -- Example: $0.10 per km
                
                IF v_payout_amount > 0 THEN
                    -- Get wallet ID
                    SELECT id INTO v_wallet_id FROM public.wallets WHERE user_id = usr.user_id;

                    -- Insert payout record
                    INSERT INTO public.cashback_payouts (user_id, payout_year, payout_month, total_distance_km, cashback_percentage, payout_amount)
                    VALUES (usr.user_id, p_year, p_month, v_total_distance, v_cashback_percentage, v_payout_amount)
                    RETURNING id INTO v_payout_id;

                    -- Update wallet balance
                    UPDATE public.wallets SET balance = balance + v_payout_amount WHERE id = v_wallet_id;
                    
                    -- Log transaction
                    INSERT INTO public.wallet_transactions(wallet_id, amount, type, description, related_payout_id)
                    VALUES(v_wallet_id, v_payout_amount, 'cashback_credit', 'Monthly cashback payout', v_payout_id);
                END IF;
            END IF;
        END IF;
    END LOOP;
END;
$$;

-- Schedule the payout function to run on the 2nd of every month for the previous month
-- Note: pg_cron uses UTC time.
SELECT cron.schedule(
    'monthly-cashback-payout',
    '0 5 2 * *', -- At 05:00 on day-of-month 2.
    $$
    SELECT public.process_monthly_cashback_payouts(
        EXTRACT(YEAR FROM (now() - INTERVAL '1 month'))::integer,
        EXTRACT(MONTH FROM (now() - INTERVAL '1 month'))::integer
    );
    $$
);
