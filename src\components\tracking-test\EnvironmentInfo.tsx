// Environment information display component
import { <PERSON><PERSON><PERSON>, User } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getEnvironmentInfo } from '@/lib/config';
import { User as AuthUser } from '@supabase/supabase-js';

interface EnvironmentInfoProps {
  user?: AuthUser | null;
  profile?: any;
}

export function EnvironmentInfo({ user, profile }: EnvironmentInfoProps) {
  const envInfo = getEnvironmentInfo();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Environment Info */}
      <Card className="bg-glass border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Settings className="h-5 w-5" />
            Environment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-white/70 text-sm">Mode:</span>
            <Badge variant="outline" className="text-white border-white/20">
              {envInfo.environment}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white/70 text-sm">Analytics:</span>
            <Badge 
              variant={envInfo.features.analytics ? "default" : "secondary"}
              className={envInfo.features.analytics ? "bg-green-600" : "bg-gray-600"}
            >
              {envInfo.features.analytics ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-white/70 text-sm">Debug:</span>
            <Badge 
              variant={envInfo.features.debug ? "default" : "secondary"}
              className={envInfo.features.debug ? "bg-blue-600" : "bg-gray-600"}
            >
              {envInfo.features.debug ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* User Info */}
      {user && (
        <Card className="bg-glass border-white/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <User className="h-5 w-5" />
              Current User
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-white/70 text-sm">
              <span className="font-medium">ID:</span> {user.id.slice(0, 8)}...
            </div>
            <div className="text-white/70 text-sm">
              <span className="font-medium">Email:</span> {user.email || 'N/A'}
            </div>
            {profile && (
              <div className="text-white/70 text-sm">
                <span className="font-medium">Username:</span> {profile.username || 'N/A'}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
