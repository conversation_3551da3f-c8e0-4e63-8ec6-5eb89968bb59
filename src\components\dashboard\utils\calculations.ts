// Utility functions for dashboard calculations

export const calculateProgress = (
  current: number,
  goal: number
): number => {
  return goal > 0 ? Math.min((current / goal) * 100, 100) : 0;
};

export const getTier = (level: number): string => {
  if (level >= 50) return 'S';
  if (level >= 30) return 'A';
  if (level >= 20) return 'B';
  if (level >= 10) return 'C';
  return 'D';
};

export const calculateWeeklyDistance = (
  activities: Array<{ distance_km: number }> | undefined
): number => {
  return activities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
};

export const calculateTodayDistance = (
  activities: Array<{ distance_km: number }> | undefined
): number => {
  return activities?.reduce((sum, activity) => sum + Number(activity.distance_km), 0) || 0;
};
