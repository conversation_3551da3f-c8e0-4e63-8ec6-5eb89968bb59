import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useTracking } from '@/hooks/useTracking';
import { logUserAction, logError } from '@/lib/logger';
import { WorkoutData } from '../types';

interface UseWorkoutSavingProps {
  userId: string | undefined;
  onSavingStateChange: (isSaving: boolean) => void;
  onWorkoutReset: () => void;
}

export function useWorkoutSaving({
  userId,
  onSavingStateChange,
  onWorkoutReset,
}: UseWorkoutSavingProps) {
  const { trackWorkoutCompletion, trackButtonClick, trackError } = useTracking();

  const saveWorkout = useCallback(async (distance: number, duration: number) => {
    if (!userId || distance <= 0) {
      trackButtonClick('stop_workout_no_save', {
        reason: distance <= 0 ? 'no_distance' : 'no_user',
        distance,
        duration
      });
      return;
    }

    onSavingStateChange(true);

    const workoutData: WorkoutData = {
      user_id: userId,
      distance_km: parseFloat(distance.toFixed(2)),
      activity_type: "Run",
      activity_date: new Date().toISOString().split("T")[0],
    };

    const { error: insertError } = await supabase.from("user_activities").insert(workoutData);
    onSavingStateChange(false);

    if (insertError) {
      const errorMsg = "Failed to save run.";
      toast.error(errorMsg, { description: insertError.message });
      trackError(errorMsg, { error: insertError.message, distance, duration });
      logError('Failed to save workout', { error: insertError, workoutData });
    } else {
      const successMsg = `You ran ${distance.toFixed(2)} km.`;
      toast.success("Run saved successfully!", { description: successMsg });

      // Track the completed workout
      trackWorkoutCompletion({
        type: 'Run',
        distance: parseFloat(distance.toFixed(2)),
        duration: duration,
      });

      logUserAction('Workout saved successfully', {
        distance: distance.toFixed(2),
        duration,
        type: 'Run'
      });

      // Reset for next run
      onWorkoutReset();
    }
  }, [userId, onSavingStateChange, onWorkoutReset, trackWorkoutCompletion, trackButtonClick, trackError]);

  return {
    saveWorkout,
  };
}
