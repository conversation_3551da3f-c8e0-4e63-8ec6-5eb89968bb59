import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export function useActivityData() {
  const { user } = useAuth();

  // Streak and level calculation
  const { data: streakData } = useQuery({
    queryKey: ['user-streak-level', user?.id],
    queryFn: async () => {
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('activities')
        .select('activity_date')
        .eq('user_id', user.id)
        .order('activity_date', { ascending: false });

      if (error) {
        console.error('Error fetching activities for streak:', error);
        return { currentStreak: 0, level: 1 };
      }

      // Calculate streak
      let currentStreak = 0;
      const today = new Date();
      const activityDates = [...new Set(data.map(a => a.activity_date))].sort().reverse();

      for (let i = 0; i < activityDates.length; i++) {
        const activityDate = new Date(activityDates[i]);
        const daysDiff = Math.floor((today.getTime() - activityDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysDiff === i) {
          currentStreak++;
        } else {
          break;
        }
      }

      // Calculate level based on total activities
      const level = Math.floor(data.length / 5) + 1;

      return { currentStreak, level };
    },
    enabled: !!user,
  });

  // Weekly activities
  const { data: weeklyActivities, isLoading: isLoadingWeekly } = useQuery({
    queryKey: ['weekly-activities', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const startOfWeek = new Date();
      startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
      startOfWeek.setHours(0, 0, 0, 0);

      const { data, error } = await supabase
        .from('activities')
        .select('distance_km')
        .eq('user_id', user.id)
        .gte('activity_date', startOfWeek.toISOString().split('T')[0]);

      if (error) {
        console.error('Error fetching weekly activities:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  // Today's activities
  const { data: todayActivities, isLoading: isLoadingToday } = useQuery({
    queryKey: ['today-activities', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const today = new Date().toISOString().split('T')[0];
      const { data, error } = await supabase
        .from('activities')
        .select('distance_km')
        .eq('user_id', user.id)
        .eq('activity_date', today);

      if (error) {
        console.error('Error fetching today activities:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  // Recent achievements
  const { data: recentAchievements } = useQuery({
    queryKey: ['recent-achievements', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('user_achievements')
        .select(`
          achievements (
            title,
            icon_name
          )
        `)
        .eq('user_id', user.id)
        .order('earned_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Error fetching recent achievements:', error);
        return [];
      }
      return data;
    },
    enabled: !!user,
  });

  return {
    streakData,
    weeklyActivities,
    isLoadingWeekly,
    todayActivities,
    isLoadingToday,
    recentAchievements,
  };
}
