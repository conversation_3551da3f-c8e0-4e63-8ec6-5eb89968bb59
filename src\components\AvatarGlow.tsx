
import React from "react";

interface AvatarGlowProps {
  src: string;
  size?: number;
  alt?: string;
  className?: string;
}

export function AvatarGlow({ src, size = 64, alt = "Avatar", className = "" }: AvatarGlowProps) {
  return (
    <div
      className={`avatar-glow mx-auto ${className}`}
      style={{
        width: size,
        height: size,
        minWidth: size,
        minHeight: size,
        position: "relative",
        overflow: "hidden",
        borderRadius: "9999px"
      }}
    >
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover"
        draggable={false}
        style={{ background: "#222" }}
      />
    </div>
  );
}
