// Performance monitoring panel
import { Monitor } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PerformanceSummary } from './types';

interface PerformancePanelProps {
  performanceSummary: PerformanceSummary;
}

export function PerformancePanel({ performanceSummary }: PerformancePanelProps) {
  return (
    <Card className="bg-black/40 border-white/10">
      <CardHeader className="pb-2">
        <CardTitle className="text-xs text-white/80 flex items-center gap-1">
          <Monitor className="h-3 w-3" />
          Performance
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1">
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Load Time:</span>
          <span className="text-xs text-white">
            {performanceSummary.averageLoadTime?.toFixed(0) || 0}ms
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Requests:</span>
          <span className="text-xs text-white">
            {performanceSummary.totalRequests || 0}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-white/60">Error Rate:</span>
          <span className="text-xs text-white">
            {((performanceSummary.errorRate || 0) * 100).toFixed(1)}%
          </span>
        </div>
        {performanceSummary.memoryUsage && (
          <div className="flex items-center justify-between">
            <span className="text-xs text-white/60">Memory:</span>
            <span className="text-xs text-white">
              {(performanceSummary.memoryUsage / 1024 / 1024).toFixed(1)}MB
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
