import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { BottomNav } from '@/components/BottomNav';
import { GlassCard } from '@/components/GlassCard';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  RefreshCw,
  ArrowLeft,
  Home
} from 'lucide-react';
import { usePaytm } from '@/hooks/usePaytm';
import { PaymentStatus } from '@/components/payments/PaymentStatus';
import { toast } from 'sonner';

export default function PaymentCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(true);
  const [callbackData, setCallbackData] = useState<Record<string, string>>({});

  // Extract all callback parameters
  useEffect(() => {
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    setCallbackData(params);
    
    // Log callback data for debugging
    console.log('Payment callback received:', params);
    
    // Stop processing after a short delay to show the callback was received
    setTimeout(() => {
      setIsProcessing(false);
    }, 1000);
  }, [searchParams]);

  const handlePaymentSuccess = () => {
    toast.success('Payment completed successfully!');
    navigate('/wallet');
  };

  const handlePaymentFailure = (error: string) => {
    toast.error(`Payment failed: ${error}`);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoToWallet = () => {
    navigate('/wallet');
  };

  const orderId = callbackData.ORDERID || callbackData.orderId;
  const status = callbackData.STATUS || callbackData.status;

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-left-top bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-4">
          {/* Header */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/payment')}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="gradient-title text-2xl">Payment Callback</h1>
              <p className="text-white/60 text-sm">Processing your payment response</p>
            </div>
          </div>

          {isProcessing ? (
            /* Processing State */
            <GlassCard className="p-8 text-center">
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-blue-500/20 border-2 border-blue-500/50 rounded-full">
                  <RefreshCw className="h-8 w-8 text-blue-400 animate-spin" />
                </div>
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">Processing Payment</h2>
              <p className="text-white/60">
                Please wait while we process your payment response...
              </p>
            </GlassCard>
          ) : orderId ? (
            /* Payment Status Component */
            <PaymentStatus
              orderId={orderId}
              onSuccess={handlePaymentSuccess}
              onFailure={handlePaymentFailure}
              autoVerify={true}
            />
          ) : (
            /* No Order ID Found */
            <GlassCard className="p-8 text-center">
              <div className="flex justify-center mb-4">
                <div className="p-4 bg-red-500/20 border-2 border-red-500/50 rounded-full">
                  <XCircle className="h-8 w-8 text-red-400" />
                </div>
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">Invalid Callback</h2>
              <p className="text-white/60 mb-6">
                No valid payment information found in the callback.
              </p>
              
              <div className="flex gap-3 justify-center">
                <Button
                  variant="outline"
                  onClick={handleGoHome}
                  className="border-white/20 text-white/80 hover:bg-white/10"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
                <Button
                  onClick={handleGoToWallet}
                  className="bg-electric hover:bg-purple text-white"
                >
                  Go to Wallet
                </Button>
              </div>
            </GlassCard>
          )}

          {/* Debug Information (only in development) */}
          {process.env.NODE_ENV === 'development' && Object.keys(callbackData).length > 0 && (
            <GlassCard className="p-4">
              <h3 className="font-semibold text-white mb-3">Debug: Callback Data</h3>
              <div className="bg-black/50 p-3 rounded border border-white/10">
                <pre className="text-xs text-white/70 overflow-x-auto">
                  {JSON.stringify(callbackData, null, 2)}
                </pre>
              </div>
            </GlassCard>
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
