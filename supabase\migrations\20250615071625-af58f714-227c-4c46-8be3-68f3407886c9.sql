
-- Create a table to store calculated cashback for users
CREATE TABLE public.user_cashback (
    user_id UUID PRIMARY KEY,
    username TEXT,
    cashback_percentage NUMERIC(5, 2) NOT NULL DEFAULT 0,
    last_calculated TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_user
      FOREIGN KEY(user_id) 
	  REFERENCES public.profiles(id)
	  ON DELETE CASCADE
);

COMMENT ON TABLE public.user_cashback IS 'Stores calculated monthly cashback for users. Updated by a trigger on user_activities.';

-- Add an index on username for faster lookups
CREATE INDEX idx_user_cashback_username ON public.user_cashback(username);

-- Enable RLS for the new table
ALTER TABLE public.user_cashback ENABLE ROW LEVEL SECURITY;

-- Allow admins to see all cashback data
CREATE POLICY "Ad<PERSON> can view all user cashback"
ON public.user_cashback FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

-- Allow users to see their own cashback
CREATE POLICY "Users can view their own cashback"
ON public.user_cashback FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Function to update a user's cashback
CREATE OR REPLACE FUNCTION public.update_user_cashback()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_to_update_id UUID;
    user_to_update_username TEXT;
    calculated_cashback NUMERIC;
BEGIN
    IF (TG_OP = 'DELETE') THEN
        user_to_update_id := OLD.user_id;
    ELSE
        user_to_update_id := NEW.user_id;
    END IF;

    -- Get username
    SELECT username INTO user_to_update_username FROM profiles WHERE id = user_to_update_id;

    -- Calculate cashback
    SELECT (SUM(get_cashback_base(ua.distance_km)) / 30.0) * 100.0 INTO calculated_cashback
    FROM user_activities ua
    WHERE ua.user_id = user_to_update_id
      AND ua.activity_date >= (CURRENT_DATE - INTERVAL '30 days');

    -- Insert or update the cashback table
    INSERT INTO public.user_cashback (user_id, username, cashback_percentage, last_calculated)
    VALUES (user_to_update_id, user_to_update_username, COALESCE(calculated_cashback, 0), now())
    ON CONFLICT (user_id) 
    DO UPDATE SET
        username = EXCLUDED.username,
        cashback_percentage = EXCLUDED.cashback_percentage,
        last_calculated = EXCLUDED.last_calculated;

    RETURN NULL;
END;
$$;

-- Trigger to update cashback on activity changes
CREATE TRIGGER on_user_activity_change
AFTER INSERT OR UPDATE OR DELETE ON public.user_activities
FOR EACH ROW
EXECUTE FUNCTION public.update_user_cashback();

-- Drop the old RPC function as it's replaced by the new table
DROP FUNCTION IF EXISTS public.calculate_monthly_cashback();
