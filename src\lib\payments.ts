// Payment utilities and validation functions
import { supabase } from '@/integrations/supabase/client';
import { PaytmTransactionResponse, PaytmTransactionStatus } from './paytm';

// Payment amount validation
export const validatePaymentAmount = (amount: number): { isValid: boolean; error?: string } => {
  if (amount <= 0) {
    return { isValid: false, error: 'Amount must be greater than 0' };
  }
  
  if (amount < 1) {
    return { isValid: false, error: 'Minimum amount is ₹1' };
  }
  
  if (amount > 100000) {
    return { isValid: false, error: 'Maximum amount is ₹1,00,000' };
  }
  
  // Check for valid decimal places (max 2)
  const decimalPlaces = (amount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    return { isValid: false, error: 'Amount can have maximum 2 decimal places' };
  }
  
  return { isValid: true };
};

// Order ID validation
export const validateOrderId = (orderId: string): { isValid: boolean; error?: string } => {
  if (!orderId || orderId.trim().length === 0) {
    return { isValid: false, error: 'Order ID is required' };
  }
  
  if (orderId.length > 50) {
    return { isValid: false, error: 'Order ID cannot exceed 50 characters' };
  }
  
  // Check for valid characters (alphanumeric, underscore, hyphen, dot)
  const validPattern = /^[a-zA-Z0-9_\-\.]+$/;
  if (!validPattern.test(orderId)) {
    return { isValid: false, error: 'Order ID contains invalid characters' };
  }
  
  return { isValid: true };
};

// Customer ID validation
export const validateCustomerId = (customerId: string): { isValid: boolean; error?: string } => {
  if (!customerId || customerId.trim().length === 0) {
    return { isValid: false, error: 'Customer ID is required' };
  }
  
  if (customerId.length > 64) {
    return { isValid: false, error: 'Customer ID cannot exceed 64 characters' };
  }
  
  return { isValid: true };
};

// Email validation
export const validateEmail = (email?: string): { isValid: boolean; error?: string } => {
  if (!email) {
    return { isValid: true }; // Email is optional
  }
  
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(email)) {
    return { isValid: false, error: 'Invalid email format' };
  }
  
  return { isValid: true };
};

// Phone number validation
export const validatePhoneNumber = (phone?: string): { isValid: boolean; error?: string } => {
  if (!phone) {
    return { isValid: true }; // Phone is optional
  }
  
  // Indian phone number pattern (10 digits)
  const phonePattern = /^[6-9]\d{9}$/;
  if (!phonePattern.test(phone.replace(/\D/g, ''))) {
    return { isValid: false, error: 'Invalid phone number format' };
  }
  
  return { isValid: true };
};

// Comprehensive payment request validation
export const validatePaymentRequest = (request: {
  amount: number;
  orderId: string;
  customerId: string;
  customerEmail?: string;
  customerPhone?: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  const amountValidation = validatePaymentAmount(request.amount);
  if (!amountValidation.isValid) {
    errors.push(amountValidation.error!);
  }
  
  const orderIdValidation = validateOrderId(request.orderId);
  if (!orderIdValidation.isValid) {
    errors.push(orderIdValidation.error!);
  }
  
  const customerIdValidation = validateCustomerId(request.customerId);
  if (!customerIdValidation.isValid) {
    errors.push(customerIdValidation.error!);
  }
  
  const emailValidation = validateEmail(request.customerEmail);
  if (!emailValidation.isValid) {
    errors.push(emailValidation.error!);
  }
  
  const phoneValidation = validatePhoneNumber(request.customerPhone);
  if (!phoneValidation.isValid) {
    errors.push(phoneValidation.error!);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Payment status utilities
export const getPaymentStatusColor = (status: PaytmTransactionStatus): string => {
  switch (status) {
    case 'TXN_SUCCESS':
      return 'text-green-400';
    case 'TXN_FAILURE':
      return 'text-red-400';
    case 'PENDING':
      return 'text-yellow-400';
    default:
      return 'text-gray-400';
  }
};

export const getPaymentStatusBadgeColor = (status: PaytmTransactionStatus): string => {
  switch (status) {
    case 'TXN_SUCCESS':
      return 'bg-green-500/20 text-green-400 border-green-500/50';
    case 'TXN_FAILURE':
      return 'bg-red-500/20 text-red-400 border-red-500/50';
    case 'PENDING':
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50';
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/50';
  }
};

// Format currency for display
export const formatCurrency = (amount: number, currency: string = 'INR'): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Format amount for Paytm (string with 2 decimal places)
export const formatAmountForPaytm = (amount: number): string => {
  return amount.toFixed(2);
};

// Check if payment is in final state
export const isPaymentFinal = (status: PaytmTransactionStatus): boolean => {
  return status === 'TXN_SUCCESS' || status === 'TXN_FAILURE';
};

// Get user-friendly payment method name
export const getPaymentMethodDisplayName = (method: string): string => {
  const methodNames: Record<string, string> = {
    'UPI': 'UPI',
    'CC': 'Credit Card',
    'DC': 'Debit Card',
    'NB': 'Net Banking',
    'WALLET': 'Paytm Wallet',
    'EMI': 'EMI',
  };
  
  return methodNames[method] || method;
};

// Retry logic for payment verification
export const retryPaymentVerification = async (
  orderId: string,
  maxRetries: number = 3,
  delayMs: number = 2000
): Promise<any> => {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const { data, error } = await supabase.functions.invoke('paytm-verify', {
        body: { orderId },
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      if (data.success) {
        return data;
      }
      
      throw new Error(data.error || 'Verification failed');
    } catch (error) {
      lastError = error as Error;
      console.warn(`Payment verification attempt ${attempt} failed:`, error);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
      }
    }
  }
  
  throw lastError || new Error('Payment verification failed after all retries');
};

// Payment analytics utilities
export const trackPaymentEvent = (event: string, data: Record<string, any>) => {
  // This can be extended to integrate with analytics services
  console.log(`Payment Event: ${event}`, data);
  
  // Example: Send to analytics service
  // analytics.track(event, data);
};

// Error handling utilities
export const getPaymentErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.error) {
    return error.error;
  }
  
  return 'An unexpected error occurred during payment processing';
};

// Network error detection
export const isNetworkError = (error: any): boolean => {
  const networkErrorMessages = [
    'network error',
    'fetch failed',
    'connection failed',
    'timeout',
    'no internet',
    'offline'
  ];
  
  const errorMessage = getPaymentErrorMessage(error).toLowerCase();
  return networkErrorMessages.some(msg => errorMessage.includes(msg));
};

// Payment security utilities
export const sanitizePaymentData = (data: Record<string, any>): Record<string, any> => {
  const sensitiveFields = ['CHECKSUMHASH', 'merchantKey', 'key'];
  const sanitized = { ...data };
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

// Local storage utilities for payment state
export const savePaymentState = (orderId: string, state: any): void => {
  try {
    const key = `payment_${orderId}`;
    localStorage.setItem(key, JSON.stringify({
      ...state,
      timestamp: Date.now()
    }));
  } catch (error) {
    console.warn('Failed to save payment state:', error);
  }
};

export const getPaymentState = (orderId: string): any | null => {
  try {
    const key = `payment_${orderId}`;
    const stored = localStorage.getItem(key);
    if (!stored) return null;
    
    const state = JSON.parse(stored);
    
    // Check if state is not too old (1 hour)
    if (Date.now() - state.timestamp > 60 * 60 * 1000) {
      localStorage.removeItem(key);
      return null;
    }
    
    return state;
  } catch (error) {
    console.warn('Failed to get payment state:', error);
    return null;
  }
};

export const clearPaymentState = (orderId: string): void => {
  try {
    const key = `payment_${orderId}`;
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to clear payment state:', error);
  }
};
