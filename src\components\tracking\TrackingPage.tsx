import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTimeTracking } from '@/hooks/useTracking';
import { BottomNav } from '@/components/BottomNav';
import { useTrackingData } from './hooks/useTrackingData';
import { useGeolocationTracking } from './hooks/useGeolocationTracking';
import { useWorkoutSaving } from './hooks/useWorkoutSaving';
import { TodayProgressCard } from './TodayProgressCard';
import { SessionMetrics } from './SessionMetrics';
import { TrackingControls } from './TrackingControls';
import { TrackingState } from './types';

export function TrackingPage() {
  const { user } = useAuth();
  
  // Track time spent on track run page
  useTimeTracking('Track Run');

  // State management
  const [trackingState, setTrackingState] = useState<TrackingState>({
    isTracking: false,
    distance: 0,
    duration: 0,
    isSaving: false,
    error: null,
  });

  // Data fetching
  const { userSettings, isLoadingSettings, todayActivities, isLoadingToday } = useTrackingData();

  // Workout saving
  const { saveWorkout } = useWorkoutSaving({
    userId: user?.id,
    onSavingStateChange: (isSaving) => setTrackingState(prev => ({ ...prev, isSaving })),
    onWorkoutReset: () => setTrackingState(prev => ({ ...prev, distance: 0, duration: 0 })),
  });

  // Geolocation tracking
  const { startTracking, stopTracking, cleanup } = useGeolocationTracking({
    onDistanceUpdate: (newDistance) => setTrackingState(prev => ({ 
      ...prev, 
      distance: prev.distance + newDistance 
    })),
    onError: (error) => setTrackingState(prev => ({ ...prev, error })),
    onStop: (shouldSave = true) => handleStop(shouldSave),
  });

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleStart = () => {
    // Reset state for a new run
    setTrackingState({
      isTracking: true,
      distance: 0,
      duration: 0,
      isSaving: false,
      error: null,
    });

    startTracking((updateFn) => {
      setTrackingState(prev => ({ 
        ...prev, 
        duration: typeof updateFn === 'function' ? updateFn(prev.duration) : updateFn 
      }));
    });
  };

  const handleStop = async (shouldSave = true) => {
    stopTracking();
    setTrackingState(prev => ({ ...prev, isTracking: false }));

    if (shouldSave) {
      await saveWorkout(trackingState.distance, trackingState.duration);
    }
  };

  const dailyGoal = userSettings?.daily_goal_km || 6;

  return (
    <div className="pb-16 pt-6 min-h-screen bg-backdrop flex flex-col">
      <div className="px-4">
        <h1 className="gradient-title text-2xl text-center font-bold mb-6">Track Your Run</h1>

        {trackingState.error && (
          <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-200 text-sm text-center">
            {trackingState.error}
          </div>
        )}

        <TodayProgressCard
          todayActivities={todayActivities}
          dailyGoal={dailyGoal}
          isLoading={isLoadingToday || isLoadingSettings}
        />

        <SessionMetrics
          distance={trackingState.distance}
          duration={trackingState.duration}
          isTracking={trackingState.isTracking}
        />

        <TrackingControls
          isTracking={trackingState.isTracking}
          isSaving={trackingState.isSaving}
          onStart={handleStart}
          onStop={() => handleStop()}
        />

        {trackingState.isTracking && (
          <div className="text-center text-white/60 text-sm">
            <p>Keep your phone with you and stay safe!</p>
            <p className="mt-1">GPS tracking is active</p>
          </div>
        )}
      </div>

      <BottomNav />
    </div>
  );
}
